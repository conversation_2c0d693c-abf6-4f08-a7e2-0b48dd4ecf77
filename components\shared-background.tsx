"use client"

import Image from "next/image"

export function SharedBackground() {
  return (
    <div className="fixed inset-0 z-0 pointer-events-none h-[100vh]">
      <div className="relative w-full h-full">
        <Image
          src="/images/hero5.avif"
          alt="Arrière-plan - Paysage algérien inspirant pour l'apprentissage"
          fill
          className="object-cover object-center"
          style={{ opacity: 1 }}
          priority
          objectPosition="0px 0px"
        />
        {/* Overlay sombre + dégradé */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/40 to-transparent" />
      </div>
    </div>
  )
} 