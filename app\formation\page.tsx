"use client";
import { motion } from "framer-motion";
import { useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  BookO<PERSON>,
  ScrollText,
  UsersRound,
  ChevronRight,
  Play,
} from "lucide-react";

const volets = [
  {
    id: "vocabulaire",
    icon: <BookOpen className="h-8 w-8" />,
    title: "Vocabulaire",
    description:
      "Enrichissez votre vocabulaire à travers des textes authentiques et des histoires algériennes captivantes.",
    color: "bg-green-100 text-green-600",
    content: {
      video: "https://drive.google.com/file/d/1tWSjWC08NZ1VwP1Pv_8HR0nUMiIk5rCt/preview",
      thumbnail: "/images/formation/Vocabulaire.avif",
      details: [
        "Textes authentiques",
        "Histoires algériennes",
        "Comptes et récits",
        "Méthode mnémotechnique",
      ],
    },
  },
  {
    id: "conjugaison",
    icon: <ScrollText className="h-8 w-8" />,
    title: "Conjugaison",
    description:
      "Maîtrisez les règles essentielles de la conjugaison de l'algérien avec des exercices pratiques et progressifs.",
    color: "bg-blue-100 text-blue-600",
    content: {
      video: "https://drive.google.com/file/d/1soKi05t8eh9B_XtpHOTWT-9Wk4uxSkAt/preview",
      thumbnail: "/images/formation/Conjugaison.avif",
      details: [
        "Règles simplifiées",
        "Exercices progressifs",
        "Tableaux de conjugaison",
        "Cas pratiques",
      ],
    },
  },
  {
    id: "contexte",
    icon: <UsersRound className="h-8 w-8" />,
    title: "Contexte",
    description:
      "Apprenez à travers des situations authentiques de la vie quotidienne et des dialogues naturels.",
    color: "bg-purple-100 text-purple-600",
    content: {
      video: "https://drive.google.com/file/d/1kb2SYClEpzsfBwWbl0JaFsOCZTfMzhwp/preview",
      thumbnail: "/images/formation/Contextes.avif",
      details: [
        "Situations réelles",
        "Dialogues authentiques",
        "Expressions courantes",
        "Mises en pratique",
      ],
    },
  },
];

// Composant pour l'aperçu vidéo avec image
function VideoPreview({ volet, index }: { volet: any; index: number }) {
  const [showVideo, setShowVideo] = useState(false);

  if (showVideo) {
    return (
      <div className="aspect-video w-full overflow-hidden rounded-lg shadow-lg">
        <iframe
          width="100%"
          height="100%"
          src={volet.content.video}
          title={volet.title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          className="border-0"
          aria-label={`Vidéo volet : ${volet.title}`}
        ></iframe>
      </div>
    );
  }

  return (
    <div className="relative aspect-video w-full overflow-hidden rounded-lg shadow-lg cursor-pointer group">
      <Image
        src={volet.content.thumbnail}
        alt={`Aperçu ${volet.title}`}
        fill
        className="object-cover transition-transform duration-300 group-hover:scale-105"
        sizes="(max-width: 768px) 100vw, 50vw"
      />
      <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors duration-300" />
      <button
        onClick={() => setShowVideo(true)}
        className="absolute inset-0 flex items-center justify-center group"
        aria-label={`Lire la vidéo ${volet.title}`}
      >
        <div className="bg-white/90 rounded-full p-4 group-hover:bg-white group-hover:scale-110 transition-all duration-300 shadow-lg">
          <Play className="h-8 w-8 text-gray-800 ml-1" />
        </div>
      </button>
    </div>
  );
}

export default function FormationPage() {
  const sectionsRef = useRef<(HTMLElement | null)[]>([]);

  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative rounded-lg flex flex-col items-center justify-center min-h-[20vh] z-10">
        <div className="text-center space-y-8 w-full">
          <motion.h1
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="text-4xl md:text-6xl font-extrabold tracking-tight text-white drop-shadow-hero mb-4"
          >
            Les Trois Volets de notre formation
          </motion.h1>
        </div>
      </section>

      {/* Detailed Sections */}
      {volets.map((volet, index) => (
        <motion.section
          key={volet.id}
          ref={(el: HTMLElement | null) => {
            if (el) sectionsRef.current[index] = el;
          }}
          id={volet.id}
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: index * 0.2 }}
          className={`py-16 px-4 bg-gradient-to-b from-white to-gray-50 ${index === 0 ? "rounded-t-lg" : ""}`}
        >
          <div className="container mx-auto">
            <div className="flex flex-col gap-8 items-start w-full">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 + 0.3 }}
                className="w-full"
              >
                <div className={`p-3 rounded-full w-fit mb-4 ${volet.color}`}>
                  {volet.icon}
                </div>
                <h2 className="text-3xl font-bold mb-4">{volet.title}</h2>
                <p className="text-lg text-muted-foreground mb-6">
                  {volet.description}
                </p>
                <div className="grid grid-cols-2 gap-4 mb-8">
                  {volet.content.details.map((detail, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{
                        duration: 0.4,
                        delay: index * 0.2 + 0.4 + i * 0.1,
                      }}
                      className="flex items-center gap-2 text-muted-foreground"
                    >
                      <ChevronRight className="h-4 w-4 text-primary" />
                      <span>{detail}</span>
                    </motion.div>
                  ))}
                </div>
                {/* Vidéo sous la description, taille réduite et centrée */}
                <div className="w-full flex justify-center mt-4">
                  <div className="w-full max-w-4xl">
                    <VideoPreview volet={volet} index={index} />
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>
      ))}

      {/* Section Bonus : Formation enrichie */}
      <motion.section
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8, delay: 1.0 }}
        className="py-16 px-4 bg-gradient-to-b from-white to-green-50"
      >
        <div className="container mx-auto flex flex-col items-center justify-center">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-4 text-green-700 flex items-center gap-3 justify-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <span role="img" aria-label="étoile">✨</span>
            Une formation encore plus riche !
          </motion.h2>
          <motion.p
            className="text-lg text-muted-foreground mb-6 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            En plus des volets principaux, notre formation propose une expérience ludique et interactive pour booster votre apprentissage :
          </motion.p>

          {/* Section détaillée des bénéfices */}
          <motion.div
            className="max-w-4xl mx-auto mb-8"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="grid gap-6 md:grid-cols-2 mb-8">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-xl font-semibold text-green-700 mb-3">Apprentissage Progressif</h3>
                <p className="text-muted-foreground">
                  Notre méthode en 3 volets vous permet d'acquérir l'algérien de manière structurée et logique.
                  Chaque étape s'appuie sur la précédente pour construire une base solide et durable.
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-xl font-semibold text-green-700 mb-3">Immersion Culturelle</h3>
                <p className="text-muted-foreground">
                  Au-delà de la langue, découvrez la richesse de la culture algérienne à travers des contextes
                  authentiques, des expressions populaires et des situations de la vie quotidienne.
                </p>
              </div>
            </div>
          </motion.div>

          <ul className="list-disc pl-0 space-y-2 text-base text-green-900 text-center">
            <motion.li
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="inline-block"
            >
              <strong>Quiz interactifs</strong> pour tester et renforcer vos acquis à chaque étape.
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              className="inline-block"
            >
              <strong>Jeux pédagogiques</strong> pour apprendre en s'amusant et mémoriser durablement.
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 1.0 }}
              className="inline-block"
            >
              <strong>Devinettes et challenges</strong> pour stimuler votre curiosité et progresser de façon originale.
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 1.2 }}
              className="inline-block"
            >
              <strong>Exercices pratiques</strong> et mises en situation pour appliquer concrètement vos connaissances.
            </motion.li>
            
          </ul>
          <motion.p
            className="mt-6 text-base text-green-800 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.6 }}
          >
            Notre objectif : rendre l'apprentissage de l'algérien vivant, motivant et accessible à tous, quel que soit votre niveau !
          </motion.p>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section
        id="6"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8, delay: 1.2 }}
        className="py-16 px-4 bg-gradient-to-b from-gray-50 to-white"
      >
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Prêt à commencer votre apprentissage ?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Rejoignez notre communauté d'apprenants et commencez votre voyage
            vers la maîtrise de l'algérien dès aujourd'hui.
          </p>
          <Link href="/formules">
            <Button size="lg" className="text-lg px-8 py-6">
              Commencer maintenant
              <ChevronRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </motion.section>
    </div>
  );
}
