"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogT<PERSON>le,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { GoogleReCaptchaProvider, useGoogleReCaptcha } from "react-google-recaptcha-v3"
import { useState } from "react"
import dynamic from "next/dynamic"
import { Mail, ExternalLink, AlertTriangle, RefreshCw } from "lucide-react"
import { CONTACT_CONFIG, generateMailtoLink, getErrorMessage } from "@/lib/contact-config"

const ReactConfetti = dynamic(() => import('react-confetti'), {
  ssr: false
})

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Le nom doit contenir au moins 2 caractères.",
  }),
  email: z.string().email({
    message: "Veuillez entrer une adresse email valide.",
  }),
  message: z.string().min(10, {
    message: "Le message doit contenir au moins 10 caractères.",
  }),
})

// Composant interne de formulaire pour accéder au hook reCAPTCHA
function ContactForm({
  onClose,
  triggerText,
  buttonVariant
}: { 
  onClose: () => void,
  triggerText: React.ReactNode,
  buttonVariant: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
}) {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const router = useRouter();
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      message: "",
    },
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [showEmailFallback, setShowEmailFallback] = useState(false)
  const [canRetry, setCanRetry] = useState(true)

  const handleRetry = () => {
    setError(null)
    setShowEmailFallback(false)
    setRetryCount(0)
    setCanRetry(true)
  }

  const handleEmailFallback = () => {
    const values = form.getValues()
    const mailtoLink = generateMailtoLink(values.name, values.email, values.message)
    window.open(mailtoLink, '_blank')
  }

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!executeRecaptcha) {
      const errorInfo = getErrorMessage(new Error("reCAPTCHA service unavailable"), retryCount);
      setError(errorInfo.message);
      setShowEmailFallback(errorInfo.showFallback);
      setCanRetry(errorInfo.canRetry);
      return;
    }

    setIsSubmitting(true)
    setError(null)
    setShowEmailFallback(false)

    try {
      const recaptchaToken = await executeRecaptcha('contact_form');

      // Ajouter un timeout à la requête
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), CONTACT_CONFIG.form.timeoutDuration);

      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name,
          email: values.email,
          message: values.message,
          recaptchaToken
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Une erreur est survenue lors de l\'envoi du message.');
      }

      // Réinitialiser les compteurs en cas de succès
      setRetryCount(0)
      setShowEmailFallback(false)
      setCanRetry(true)

      // Fermer le dialogue et rediriger vers la page de succès
      onClose();
      router.push('/contact/success');
    } catch (err) {
      console.error("Error sending email:", err);
      const newRetryCount = retryCount + 1;
      setRetryCount(newRetryCount);

      // Utiliser la fonction centralisée pour déterminer le message d'erreur
      const errorInfo = getErrorMessage(err, newRetryCount);

      setError(errorInfo.message);
      setShowEmailFallback(errorInfo.showFallback);
      setCanRetry(errorInfo.canRetry);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm space-y-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="font-medium mb-1">Erreur d'envoi</p>
                <p>{error}</p>
              </div>
            </div>

            {/* Information de contact direct - affiché uniquement en cas d'erreur */}
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-800">
                <Mail className="w-4 h-4" />
                <span className="text-sm font-medium">Contact direct :</span>
                <a
                  href={`mailto:${CONTACT_CONFIG.email}`}
                  className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {CONTACT_CONFIG.email}
                </a>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t border-red-200">
              {canRetry && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleRetry}
                  className="flex items-center gap-2 text-red-700 border-red-300 hover:bg-red-100"
                >
                  <RefreshCw className="w-3 h-3" />
                  Réessayer
                </Button>
              )}
              {showEmailFallback && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleEmailFallback}
                  className="flex items-center gap-2 text-blue-700 border-blue-300 hover:bg-blue-100"
                >
                  <Mail className="w-3 h-3" />
                  Envoyer par email
                  <ExternalLink className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>
        )}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[#8dc63f]">Nom</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Votre nom" 
                  {...field} 
                  className="focus:ring-[#8dc63f] focus:border-[#8dc63f] focus:outline-none"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[#8dc63f]">Email</FormLabel>
              <FormControl>
                <Input 
                  placeholder="<EMAIL>" 
                  {...field} 
                  className="focus:ring-[#8dc63f] focus:border-[#8dc63f] focus:outline-none"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-[#8dc63f]">Message</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Votre message..." 
                  className="resize-none focus:ring-[#8dc63f] focus:border-[#8dc63f] focus:outline-none" 
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="text-xs text-muted-foreground flex flex-col sm:flex-row sm:items-center mt-4">
          <div className="flex flex-wrap items-center gap-1">
            <span>Ce site est protégé par reCAPTCHA et les</span>
            <a 
              href="https://policies.google.com/privacy" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-[#8dc63f] hover:underline"
            >
              Règles de confidentialité
            </a>
            <span>et</span>
            <a 
              href="https://policies.google.com/terms" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-[#8dc63f] hover:underline"
            >
              Conditions
            </a>
            <span>de Google s'appliquent.</span>
          </div>
          <div className="mt-2 sm:mt-0 sm:ml-2 flex justify-end">
            <svg width="28" height="28" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">
              <g fill="none" fillRule="evenodd">
                <path d="M14 28c7.732 0 14-6.268 14-14S21.732 0 14 0 0 6.268 0 14s6.268 14 14 14z" fill="#FFF"/>
                <path d="M13.296 10.885v2.7h4.092c-.24 1.1-1.2 3.096-4.092 3.096-2.46 0-4.464-2.04-4.464-4.56 0-2.52 2.004-4.56 4.464-4.56 1.392 0 2.34.6 2.856 1.116l1.968-1.884c-1.272-1.188-2.904-1.896-4.824-1.896-3.972 0-7.2 3.24-7.2 7.224s3.228 7.224 7.2 7.224c4.14 0 6.924-2.928 6.924-7.032 0-.48-.048-.84-.12-1.2l-6.804-.228z" fill="#737373"/>
              </g>
            </svg>
          </div>
        </div>
        
        <DialogFooter className="sm:justify-end">
          <Button 
            type="submit" 
            disabled={isSubmitting} 
            className="w-full sm:w-auto bg-[#8dc63f] hover:bg-[#6e9a32] transition-colors duration-200"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Envoi en cours...</span>
              </div>
            ) : "Envoyer"}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}

export function ContactDialog({ 
  triggerText = "Nous contacter", 
  buttonVariant = "default" 
}: { 
  triggerText?: React.ReactNode,
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
}) {
  const [open, setOpen] = React.useState(false)
  const [showConfetti, setShowConfetti] = React.useState(false)

  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"}
      scriptProps={{
        async: true,
        defer: true,
        appendTo: 'head',
      }}
    >
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          {typeof triggerText === 'string' ? (
            <Button variant={buttonVariant} className="relative group">
              {triggerText}
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#8dc63f] transition-all group-hover:w-full"></span>
            </Button>
          ) : (
            triggerText
          )}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          {showConfetti && (
            <ReactConfetti
              width={typeof window !== 'undefined' ? window.innerWidth : 0}
              height={typeof window !== 'undefined' ? window.innerHeight : 0}
              recycle={false}
              numberOfPieces={500}
              gravity={0.3}
            />
          )}
          <div className="flex flex-col">
            <DialogHeader className="w-full border-b pb-4">
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center">
                  <Image
                    src="/images/logofooter2.svg"
                    alt="Logo Parler Algérien"
                    width={400}
                    height={60}
                    className="object-contain"
                  />
                </div>
                <div className="flex flex-col items-end space-y-1">
                  <DialogTitle className="text-xl font-bold text-[#8dc63f]">Contactez-nous</DialogTitle>
                  <DialogDescription className="text-sm text-gray-500 text-right">
                    Envoyez-nous un message et nous vous répondrons dans les plus brefs délais.
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>

            <div className="mt-6">
              <ContactForm
                onClose={() => {
                  setOpen(false);
                  setShowConfetti(true);
                  setTimeout(() => setShowConfetti(false), 5000);
                }}
                triggerText={triggerText}
                buttonVariant={buttonVariant}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </GoogleReCaptchaProvider>
  )
} 