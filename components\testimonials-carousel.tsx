"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import { LazyMotion, domAnimation, m, AnimatePresence, motion } from "framer-motion"
import { Pause, Play, ChevronLeft, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

// Séparation des données statiques pour améliorer la performance
const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    profession: "Enseignante universitaire",
    country: "Royaume-Uni",
    countryFlag: "/images/flags/uk.png",
    image: "/images/testimonials/user1.avif",
    content:
      "J'ai commencé à apprendre l'algérien avec Djamel Mrah après le confinement en 2020. La méthode de Djamel est basée sur sa profonde compréhension de la langue. Il est structuré, inspirant, holistique et amusant. Nous avons tous apprécié chaque leçon et, surtout, appris tellement plus sur la culture, l'histoire et les traditions algériennes.",
  },
  {
    id: 2,
    name: "<PERSON>",
    profession: "Directeur entreprise tourisme",
    country: "États-Unis",
    countryFlag: "/images/flags/usa.png",
    image: "/images/testimonials/user2.avif",
    content:
      "Djamel Mrah est l'un des meilleurs professeurs que vous puissiez demander. Le matériel d'apprentissage de Djamel est axé sur la qualité. Dans ses méthodes d'enseignement, il utilise des vidéos, des documents, des images et d'autres présentations. Si quelqu'un veut apprendre l'arabe parlé algérien, Djamel est le premier professeur à suivre.",
  },
  {
    id: 3,
    name: "Jessica Grunenfelder",
    profession: "Analyste biomédical",
    country: "Suisse",
    countryFlag: "/images/flags/ch.png",
    image: "/images/testimonials/user3.avif",
    content:
      "En cherchant un moyen d'apprendre \"l'algérien\", je suis tombé sur les premières vidéos de Djamel. On sent qu'il aime ce métier et qu'il veut transmettre beaucoup de son pays, sa cuisine, ses traditions, son histoire etc. Avec sa nature empathique et son esprit, l'apprentissage est vraiment amusant et vous faites rapidement de grands progrès.",
  },
  {
    id: 4,
    name: "Nassem Hamdan",
    profession: "Dentiste",
    country: "Royaume-Uni",
    countryFlag: "/images/flags/uk.png",
    image: "/images/testimonials/user4.avif",
    content:
      "Djamel est un grand tuteur de la langue algérienne, d'avoir étudié avec lui, il est clair qu'il est passionné par ce qu'il fait et cela le rend si facile à apprendre et à être confiant dans la parole. Il développe une relation unique avec tous ses élèves et est très patient quel que soit votre niveau !",
  },
  {
    id: 5,
    name: "Luca",
    profession: "Etudiante en anthropologie / Spécialité les réseaux sociaux en Algérie ",
    country: "Pays-Bas",
    countryFlag: "/images/flags/paybas.png",
    image: "/images/testimonials/user5.avif",
    content:
      "Djamel est un instituteur fantastique. Il est patient, il s'exprime clairement et calmement, il est bien informé sur de nombreux sujets algériens. Je recommande de prendre des cours avec lui. Chaque semaine nous discutions de la grammaire, vocabulaire et syntaxe. Comme étudiante, je pouvais choisir la thématique des conversations et à cette raison j'étais bien préparée pour mes recherches",
  },
];

const moons = [
  { size: 6, delay: 0, duration: 1.5 },
  { size: 4, delay: 0.3, duration: 2 },
  { size: 5, delay: 0.7, duration: 1.8 },
];

// Configuration des variantes d'animation pour les transitions du carousel
const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
};

export function TestimonialsCarousel() {
  // États pour gérer le carousel
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoScrolling, setIsAutoScrolling] = useState(true)
  const autoScrollRef = useRef<NodeJS.Timeout | null>(null)
  const [direction, setDirection] = useState(0)

  // Fonction pour démarrer le défilement automatique
  const startAutoScroll = () => {
    if (autoScrollRef.current) {
      clearInterval(autoScrollRef.current)
    }

    // Configuration de l'intervalle de défilement automatique
    autoScrollRef.current = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
    }, 5000) // Changement de témoignage toutes les 5 secondes
  }

  // Fonction pour arrêter le défilement automatique
  const stopAutoScroll = () => {
    if (autoScrollRef.current) {
      clearInterval(autoScrollRef.current)
      autoScrollRef.current = null
    }
  }

  // Fonction pour basculer le défilement automatique
  const toggleAutoScroll = () => {
    setIsAutoScrolling((prev) => !prev)
  }

  // Fonction pour naviguer vers un témoignage spécifique
  const goToTestimonial = (index: number) => {
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index)
    if (isAutoScrolling) {
      stopAutoScroll()
      startAutoScroll()
    }
  }

  // Fonction pour aller au témoignage précédent
  const goToPrevious = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
    if (isAutoScrolling) {
      stopAutoScroll()
      startAutoScroll()
    }
  }

  // Fonction pour aller au témoignage suivant
  const goToNext = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
    if (isAutoScrolling) {
      stopAutoScroll()
      startAutoScroll()
    }
  }

  // Effet pour gérer le défilement automatique
  useEffect(() => {
    if (isAutoScrolling) {
      startAutoScroll()
    } else {
      stopAutoScroll()
    }

    return () => {
      stopAutoScroll()
    }
  }, [isAutoScrolling])

  return (
    <section id="eleves" className="w-full py-4 md:py-6 lg:py-8 bg-gradient-to-b from-gray-50 to-white overflow-hidden">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-2">
          <h2 className="text-3xl font-bold tracking-tighter md:text-4xl mb-2">Ce qu'ils disent</h2>
          <p className="text-muted-foreground md:text-lg max-w-3xl mx-auto">
            Découvrez les témoignages de nos apprenants du monde entier
          </p>
        </div>

        <LazyMotion features={domAnimation}>
          <div className="relative max-w-4xl mx-auto">
            {/* Navigation buttons */}
            <div className="absolute top-1/2 left-0 -translate-y-1/2 -translate-x-4 z-10 hidden md:block">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full bg-white shadow-md hover:bg-[#6e9a32] border-[#8dc63f] text-[#8dc63f] hover:text-white hover:border-[#6e9a32]"
                onClick={goToPrevious}
              >
                <ChevronLeft className="h-6 w-6" />
                <span className="sr-only">Témoignage précédent</span>
              </Button>
            </div>
            <div className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-4 z-10 hidden md:block">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full bg-white shadow-md hover:bg-[#6e9a32] border-[#8dc63f] text-[#8dc63f] hover:text-white hover:border-[#6e9a32]"
                onClick={goToNext}
              >
                <ChevronRight className="h-6 w-6" />
                <span className="sr-only">Témoignage suivant</span>
              </Button>
            </div>

            {/* Testimonial cards */}
            <div className="relative h-[670px] md:h-[350px]">
              <AnimatePresence initial={false} custom={direction} mode="wait">
                <m.div
                  key={currentIndex}
                  custom={direction}
                  variants={variants}
                  initial="enter"
                  animate="center"
                  exit="exit"
                  transition={{
                    x: { type: "spring", stiffness: 300, damping: 30 },
                    opacity: { duration: 0.2 },
                  }}
                  className="absolute w-full"
                >
                  <div className="bg-white rounded-2xl shadow-xl md:h-[350px] h-[670px] overflow-hidden ring-2 ring-[#8dc63f]/40 shadow-[0_0_15px_rgba(141,198,63,0.5)]">
                    <div className="flex flex-col md:flex-row h-full">
                      <div className="flex-shrink-0 w-full md:w-2/5 relative h-[280px] md:h-full p-2">
                        <Image
                          src={testimonials[currentIndex].image || "/placeholder.svg"}
                          alt={testimonials[currentIndex].name}
                          fill
                          sizes="(max-width: 768px) 100vw, 40vw"
                          className="object-cover"
                          loading="eager"
                          priority={currentIndex === 0}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent md:bg-gradient-to-r md:from-transparent md:to-black/10"></div>
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent md:hidden">
                          <h3 className="text-xl font-bold text-white">{testimonials[currentIndex].name}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Image
                              src={testimonials[currentIndex].countryFlag || "/placeholder.svg"}
                              alt={testimonials[currentIndex].country}
                              width={20}
                              height={14}
                              className="rounded-sm"
                              loading="eager"
                            />
                            <span className="text-sm text-white">{testimonials[currentIndex].country}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex-grow p-6 md:p-8 text-center md:text-left flex flex-col justify-center relative overflow-y-auto max-h-[400px] md:max-h-full bg-white">
                        <div className="hidden md:block mb-4">
                          <h3 className="text-xl font-bold">{testimonials[currentIndex].name}</h3>
                          <p className="text-muted-foreground">{testimonials[currentIndex].profession}</p>
                          <div className="flex items-center gap-2 mt-1 justify-center md:justify-start">
                            <Image
                              src={testimonials[currentIndex].countryFlag || "/placeholder.svg"}
                              alt={testimonials[currentIndex].country}
                              width={20}
                              height={14}
                              className="rounded-sm"
                              loading="eager"
                            />
                            <span className="text-sm text-muted-foreground">{testimonials[currentIndex].country}</span>
                          </div>
                        </div>
                        <div className="mt-2 md:mt-4">
                          <p className="italic text-gray-700 leading-relaxed">"
                            {testimonials[currentIndex].content}
                          "</p>
                        </div>
                        <div className="md:hidden mt-2">
                          <p className="text-muted-foreground">{testimonials[currentIndex].profession}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </m.div>
              </AnimatePresence>
            </div>

            {/* Controls */}
            <div className="flex flex-col items-center mt-8 gap-4">
              <Button
                variant="outline"
                size="sm"
                className="rounded-full border-[#8dc63f] text-[#8dc63f] hover:bg-[#6e9a32] hover:text-white hover:border-[#6e9a32]"
                onClick={toggleAutoScroll}
                aria-label={
                  isAutoScrolling ? "Mettre en pause le défilement automatique" : "Activer le défilement automatique"
                }
              >
                {isAutoScrolling ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                {isAutoScrolling ? "Pause" : "Lecture automatique"}
              </Button>

              <div className="flex gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentIndex ? "bg-[#8dc63f]" : "bg-gray-300 hover:bg-[#6e9a32]"
                    }`}
                    aria-label={`Voir témoignage ${index + 1}`}
                    aria-current={index === currentIndex ? "true" : "false"}
                  />
                ))}
              </div>
            </div>
          </div>
          <motion.div className="flex flex-col items-center text-center space-y-8 pt-3" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link href="/formules">
                  <Button variant="default" className="relative group overflow-hidden">
                    <span>Rejoignez-les</span>
                    
                    {/* Croissants animées */}
                    <div className="inline-flex relative ml-1 mr-4">
                      {moons.map((star, index) => (
                        <motion.div
                          key={index}
                          className="absolute"
                          initial={{ opacity: 0.8, scale: 0 }}
                          animate={{
                            opacity: [0.8, 1, 0.8],
                            scale: [0, 1, 0],
                            x: [0, 2, 0],
                            y: [0, -3, 0]
                          }}
                          transition={{
                            duration: star.duration,
                            repeat: Infinity,
                            delay: star.delay,
                            ease: "easeInOut"
                          }}
                          style={{
                            top: index % 2 === 0 ? "-2px" : "-10px",
                            left: `${index * 7}px`,
                            width: `${star.size}px`,
                            height: `${star.size}px`,
                          }}
                        >
                          <svg viewBox="0 0 24 24" fill="currentColor" className="text-red-500 w-full h-full">
                          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                          </svg>
                          
                        </motion.div>
                      ))}
                    </div>
                    
                  </Button>
                </Link>
              </motion.div>
        </LazyMotion>
      </div>
    </section>
  )
}
