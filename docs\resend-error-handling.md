# 🚨 Gestion des Erreurs Resend

## 📋 Types d'Erreurs Resend Gérées

### 1. **Erreur de Configuration Test**
```json
{
  "success": true,
  "data": null,
  "error": {
    "statusCode": 403,
    "error": "You can only send testing emails to your own email address (<EMAIL>). To send emails to other recipients, please verify a domain at resend.com/domains, and change the 'from' address to an email using this domain."
  }
}
```
**Message utilisateur :** "Configuration email en mode test. Veuillez contacter l'administrateur."

### 2. **Erreur de Domaine Non Vérifié**
```json
{
  "error": {
    "statusCode": 403,
    "error": "Domain not verified. Please verify your domain at resend.com/domains"
  }
}
```
**Message utilisateur :** "Domaine email non vérifié. Veuillez contacter l'administrateur."

### 3. **Erreur d'API Key**
```json
{
  "error": {
    "statusCode": 401,
    "error": "Invalid API key"
  }
}
```
**Message utilisateur :** "Erreur de configuration du service d'email. Veuillez réessayer plus tard."

### 4. **Erreur de Rate Limit**
```json
{
  "error": {
    "statusCode": 429,
    "error": "Rate limit exceeded. Please try again later."
  }
}
```
**Message utilisateur :** "Limite d'envoi atteinte. Veuillez patienter quelques minutes avant de réessayer."

### 5. **Erreur d'Email Invalide**
```json
{
  "error": {
    "statusCode": 400,
    "error": "Invalid email address"
  }
}
```
**Message utilisateur :** "Adresse email invalide. Veuillez vérifier votre adresse email."

## 🔧 Implémentation

### API Route (`app/api/send-email/route.ts`)

```typescript
// Vérifier si Resend a retourné une erreur
if (data.error) {
  console.error("Resend API error:", data.error);
  
  const resendError = data.error;
  let errorMessage = "Erreur lors de l'envoi de l'email";
  let statusCode = 500;
  
  if (resendError.message) {
    const errorMsg = resendError.message.toLowerCase();
    
    if (errorMsg.includes("testing emails") || errorMsg.includes("own email address")) {
      errorMessage = "Configuration email en mode test. Veuillez contacter l'administrateur.";
      statusCode = 503;
    } else if (errorMsg.includes("domain") && errorMsg.includes("verify")) {
      errorMessage = "Domaine email non vérifié. Veuillez contacter l'administrateur.";
      statusCode = 503;
    }
    // ... autres conditions
  }
  
  return NextResponse.json({ error: errorMessage }, { status: statusCode });
}

// Vérifier que l'email a bien été envoyé
if (!data.data || !data.data.id) {
  return NextResponse.json(
    { error: "L'email n'a pas pu être envoyé. Veuillez réessayer." },
    { status: 500 }
  );
}
```

### Configuration Centralisée (`lib/contact-config.ts`)

```typescript
errorMessages: {
  // Messages standards
  network: "Problème de connexion...",
  timeout: "La requête a pris trop de temps...",
  rateLimit: "Trop de tentatives d'envoi...",
  
  // Messages spécifiques à Resend
  resendConfig: "Configuration email en mode test. Veuillez contacter l'administrateur.",
  resendDomain: "Domaine email non vérifié. Veuillez contacter l'administrateur.",
  resendAuth: "Erreur de configuration du service d'email. Veuillez réessayer plus tard.",
}
```

## 🎯 Comportement par Type d'Erreur

| Type d'erreur | Peut réessayer | Affiche email fallback | Code HTTP |
|---------------|----------------|------------------------|-----------|
| **Test Mode** | ❌ Non | ✅ Oui | 503 |
| **Domaine** | ❌ Non | ✅ Oui | 503 |
| **API Key** | ❌ Non | ✅ Oui | 503 |
| **Rate Limit** | ❌ Non | ✅ Oui | 429 |
| **Email Invalide** | ❌ Non | ❌ Non | 400 |
| **Réseau** | ✅ Oui | Après 2 tentatives | 503 |
| **Timeout** | ✅ Oui | Après 2 tentatives | 503 |

## 🧪 Tests Recommandés

### 1. **Test Mode Resend**
- Utiliser une API key de test
- Envoyer à un email différent de celui configuré
- Vérifier le message d'erreur approprié

### 2. **Domaine Non Vérifié**
- Configurer un domaine non vérifié dans Resend
- Tenter l'envoi d'email
- Vérifier la gestion d'erreur

### 3. **API Key Invalide**
- Utiliser une API key incorrecte
- Vérifier le message d'erreur de configuration

### 4. **Rate Limiting**
- Envoyer plusieurs emails rapidement
- Vérifier la limitation et le message approprié

## 🔍 Debug en Développement

En mode développement, l'API retourne des informations supplémentaires :

```json
{
  "error": "Configuration email en mode test...",
  "resendError": {
    "statusCode": 403,
    "error": "You can only send testing emails..."
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 📝 Logs

Tous les erreurs Resend sont loggées avec :
```typescript
console.error("Resend API error:", data.error);
```

Cela permet de diagnostiquer les problèmes en production tout en affichant des messages utilisateur appropriés.
