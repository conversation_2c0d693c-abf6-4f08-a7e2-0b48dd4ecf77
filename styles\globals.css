@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    
    /* Nouvelles couleurs des boutons */
    --primary-color: 84 68% 56%; /* #8dc63f - Vert foncé pour le bouton Accueil */
    --primary-dark: 84 51% 40%; /* #6e9a32 - Vert très foncé pour hover */
    --secondary-color: 355 86% 52%; /* #ee2c38 - Rouge du logo */
    --secondary-dark: 355 76% 47%; /* #d01c29 - Rouge foncé pour le hover */
    --accent-color: 84 68% 56%; /* #8dc63f - Vert clair du logo */
    --accent-dark: 84 51% 40%; /* #75a534 - Vert clair foncé pour hover */

    /* Mises à jour des variables existantes pour utiliser les nouvelles couleurs */
    --primary: var(--primary-color);
    --primary-foreground: 0 0% 98%;
    --secondary: var(--secondary-color);
    --secondary-foreground: 0 0% 98%;
    --accent: var(--accent-color);
    --accent-foreground: 0 0% 98%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    
    /* Nouvelles couleurs des boutons en mode sombre */
    --primary-color: 84 68% 56%; /* #8dc63f - Même couleur en mode sombre */
    --primary-dark: 84 51% 40%; /* #6e9a32 */
    --secondary-color: 355 86% 52%; /* #ee2c38 */
    --secondary-dark: 355 76% 47%; /* #d01c29 */
    --accent-color: 84 68% 56%; /* #8dc63f */
    --accent-dark: 84 51% 40%; /* #75a534 */

    /* Mises à jour des variables existantes pour utiliser les nouvelles couleurs */
    --primary: var(--primary-color);
    --primary-foreground: 0 0% 98%;
    --secondary: var(--secondary-color);
    --secondary-foreground: 0 0% 98%;
    --accent: var(--accent-color);
    --accent-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Classes personnalisées pour les boutons */
.btn-primary {
  background-color: #8dc63f !important;
  color: white !important;
}
.btn-primary:hover {
  background-color: #6e9a32 !important;
}

.btn-secondary {
  background-color: #ee2c38 !important;
  color: white !important;
}
.btn-secondary:hover {
  background-color: #d01c29 !important;
}

.btn-accent {
  background-color: #8dc63f !important;
  color: white !important;
}
.btn-accent:hover {
  background-color: #75a534 !important;
}
