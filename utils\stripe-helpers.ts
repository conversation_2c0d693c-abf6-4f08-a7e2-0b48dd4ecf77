import { loadStripe } from '@stripe/stripe-js';

// Charger l'instance Stripe une seule fois
let stripePromise: Promise<any> | null = null;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');
  }
  return stripePromise;
};

export const redirectToCheckout = async ({ plan }: { plan: string }) => {
  try {
    const response = await fetch('/api/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ plan }),
    });

    const { sessionId } = await response.json();
    const stripe = await getStripe();
    
    const { error } = await stripe.redirectToCheckout({ sessionId });
    
    if (error) {
      console.error('Stripe checkout error:', error);
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('Error in redirectToCheckout:', error);
    return {
      error: 'Une erreur est survenue lors de la redirection vers la page de paiement.',
    };
  }
}; 