"use client";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { motion, useAnimation } from "framer-motion";
import { heroImageVariants } from "./animation-variants";
import Link from "next/link";
import { useEffect, useState, useRef } from "react";
import { Dancing_Script } from "next/font/google";
import { useInView } from "framer-motion";

import { Zain } from "next/font/google";
const dancing = Dancing_Script({
  subsets: ["latin"],
  weight: "700",
  display: 'swap'
});
const zain = Zain({
  subsets: ["arabic"],
  weight: "400",
  display: 'swap'
});

const recentStudents = [
  "Sebastien <PERSON>ali",
  "Bencharef Rachid",
  "<PERSON>",
  "Zayneb Lablack",
  "Krim Kamel",
  "Thomas Sibille Bilal"
];

export function HeroSection() {
  const [studentCount, setStudentCount] = useState(0);
  const [currentStudentIndex, setCurrentStudentIndex] = useState(0);
  const [showStudent, setShowStudent] = useState(false);
  const flagControls = useAnimation();
  const counterRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(counterRef, { amount: 0.5 });

  useEffect(() => {
    if (!isInView) return;
    let start = 0;
    const end = 500;
    const duration = 2500;
    const startTime = Date.now();
    function animate() {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeOut = (t: number) => t * (2 - t);
      setStudentCount(Math.round(easeOut(progress) * end));
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setStudentCount(end);
      }
    }
    animate();
  }, [isInView]);

  // Animation pour les étudiants récents
  useEffect(() => {
    if (!isInView) return;
    
    const timer = setTimeout(() => {
      setShowStudent(true);
    }, 3000); // Commence après 3 secondes

    return () => clearTimeout(timer);
  }, [isInView]);

  useEffect(() => {
    if (!showStudent) return;

    const interval = setInterval(() => {
      setShowStudent(false);
      setTimeout(() => {
        setCurrentStudentIndex((prev) => (prev + 1) % recentStudents.length);
        setShowStudent(true);
      }, 500); // Délai entre les noms
    }, 2000); // Durée d'affichage de chaque nom

    return () => clearInterval(interval);
  }, [showStudent]);

  return (
    <section className="relative rounded-lg flex flex-col items-center justify-center min-h-[60vh] md:min-h-[80vh] z-10">
      <motion.div
        animate={flagControls}
        className="absolute top-20 right-20 w-32 h-32 z-20"
        style={{
          transformOrigin: "center center",
          filter: "drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))",
        }}
      ></motion.div>
      <div className="text-center space-y-10 w-full">
        
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.4 }}
          className={`text-4xl md:text-7xl font-bold tracking-tight text-white/90 drop-shadow-hero mb-4 ${zain.className} flex items-center justify-center gap-2`}
        >
          <span style={{ color: "#fff" }}>جزايرية</span>
          <span className="flex items-center gap-1">
            <span style={{ color: "#fff" }}>نتكلم</span>
          </span>
          <span style={{ color: "#fff" }}>نتعلم</span>
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
          className={`text-5xl text-white/80 md:text-7xl drop-shadow-hero mb-8 ${dancing.className}`}
        >
          Apprendre à parler algérien
        </motion.p>
        <motion.h2
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
          className="text-4xl md:text-6xl font-semibold tracking-tight text-gray-400 drop-shadow-hero mb-4"
        >
          Apprenez, étudiez, vivez une nouvelle langue
        </motion.h2>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.7, delay: 1 }}
          className="mx-auto w-full max-w-3xl"
        >
          <div
            className="flex items-center gap-2 w-full mb-4 justify-center"
            ref={counterRef}
          >
            <div
              className="flex items-center w-full max-w-md rounded-full shadow-lg overflow-hidden relative"
              style={{ height: 64 }}
            >
              {/* Moitié gauche verte */}
              <div
                className="flex items-center pl-4 pr-2 py-2"
                style={{ background: "#218838", width: "50%", zIndex: 1 }}
              >
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div
                      key={i}
                      className="inline-block h-12 w-12 rounded-full border-2 border-white bg-muted overflow-hidden"
                    >
                      <Image
                        src={`/images/testimonials/user${i}.avif`}
                        alt={`Étudiant satisfait ${i} - Témoignage d'apprentissage de l'algérien`}
                        width={32}
                        height={32}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  ))}
                </div>
              </div>
              {/* Croissant/étoile centré sur la séparation */}
              <div
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full flex items-center justify-center"
                style={{ width: 52, height: 52, zIndex: 2 }}
              >
                <Image
                  src="/images/flags/algeria.avif"
                  alt="Drapeau Algérien"
                  width={58}
                  height={58}
                  className="inline-block"
                />
              </div>
              {/* Moitié droite blanche */}
              <div
                className="flex items-center gap-2 pl-8 pr-4 py-4"
                style={{ background: "#fff", width: "50%", zIndex: 1 }}
              >
                <span className="tabular-nums text-2xl font-bold text-green-700 animate-pulse">
                  +{studentCount}
                </span>
                <span className="ml-1 text-green-700 font-semibold">
                  étudiants inscrits
                </span>
              </div>
            </div>
          </div>

          <div className="flex justify-center w-full">
            <Link href="/formules">
              <Button className="w-full max-w-xs text-base sm:text-lg px-4 py-3 shadow-xl text-white font-bold transition-transform duration-200 hover:scale-105 hover:brightness-110">
                Commencer Maintenant <ChevronRight className="ml-2 h-6 w-6" />
              </Button>
            </Link>
          </div>

          {/* Liste des derniers étudiants inscrits */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 2 }}
            className="mt-8 text-center"
          >
            <div className="text-lg md:text-xl text-white/80 font-medium italic">
              <span>Derniers inscrits: </span>
              <motion.span
                key={currentStudentIndex}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: showStudent ? 1 : 0, y: showStudent ? 0 : -30 }}
                transition={{ 
                  duration: 0.8,
                  ease: "easeInOut"
                }}
                className="inline-block"
              >
                {recentStudents[currentStudentIndex]}
              </motion.span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
