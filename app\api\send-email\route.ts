import { NextResponse } from "next/server";
import { Resend } from "resend";

// Initialize Resend with API key from environment variables
const resend = new Resend(process.env.RESEND_API_KEY);
const emailsender = process.env.RESEND_EMAIL;
// Fonction pour vérifier le token reCAPTCHA
async function verifyRecaptcha(token: string) {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;
  
  const response = await fetch("https://www.google.com/recaptcha/api/siteverify", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: `secret=${secretKey}&response=${token}`,
  });
  
  const data = await response.json();
  
  // Version v3 retourne un score au lieu d'un boolean simple
  // Le score varie de 0.0 à 1.0, où 1.0 est très probablement un humain
  console.log("reCAPTCHA response:", data);
  
  // On considère un score de 0.5 ou plus comme une interaction humaine
  // Vous pouvez ajuster ce seuil selon vos besoins
  return {
    success: data.success && data.score >= 0.5,
    score: data.score,
    action: data.action
  };
}

export async function POST(request: Request) {
  try {
    // Parse the request body
    const { name, email, message, recaptchaToken } = await request.json();

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: "Nom, email et message sont requis" },
        { status: 400 }
      );
    }
    
    // Verify reCAPTCHA token
    if (!recaptchaToken) {
      return NextResponse.json(
        { error: "La vérification reCAPTCHA est requise" },
        { status: 400 }
      );
    }
    
    // Verify reCAPTCHA token with Google
    const recaptchaResult = await verifyRecaptcha(recaptchaToken);
    
    if (!recaptchaResult.success) {
      // Si le score est trop bas, on considère que c'est un bot
      return NextResponse.json(
        { 
          error: "La vérification reCAPTCHA a échoué. Veuillez réessayer.",
          debug: process.env.NODE_ENV === "development" ? { score: recaptchaResult.score } : undefined
        },
        { status: 400 }
      );
    }

    // Send email using Resend
    const data = await resend.emails.send({
      from: `${name} <<EMAIL>>`, // Use the default onboarding sender
      to: [emailsender!], // Replace with your actual email
      subject: `Nouveau message de ${name}`,
      replyTo: email,
      text: `Nom: ${name}\nEmail: ${email}\n\nMessage:\n${message}`,
      html: `
        <p><strong>Nom:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Message:</strong></p>
        <p>${message.replace(/\n/g, "<br>")}</p>
      `,
    });

    // Vérifier si Resend a retourné une erreur
    if (data.error) {
      console.error("Resend API error:", data.error);

      // Analyser le type d'erreur Resend et retourner un message approprié
      const resendError = data.error;
      let errorMessage = "Erreur lors de l'envoi de l'email";
      let statusCode = 500;

      if (resendError.message) {
        const errorMsg = resendError.message.toLowerCase();

        if (errorMsg.includes("testing emails") || errorMsg.includes("own email address")) {
          errorMessage = "Configuration email en mode test. Veuillez contacter l'administrateur.";
          statusCode = 503;
        } else if (errorMsg.includes("domain") && errorMsg.includes("verify")) {
          errorMessage = "Domaine email non vérifié. Veuillez contacter l'administrateur.";
          statusCode = 503;
        } else if (errorMsg.includes("api key") || errorMsg.includes("unauthorized")) {
          errorMessage = "Erreur de configuration du service d'email. Veuillez réessayer plus tard.";
          statusCode = 503;
        } else if (errorMsg.includes("rate limit") || errorMsg.includes("quota")) {
          errorMessage = "Limite d'envoi atteinte. Veuillez patienter quelques minutes avant de réessayer.";
          statusCode = 429;
        } else if (errorMsg.includes("invalid") && errorMsg.includes("email")) {
          errorMessage = "Adresse email invalide. Veuillez vérifier votre adresse email.";
          statusCode = 400;
        } else {
          errorMessage = `Erreur du service d'email: ${resendError.message}`;
        }
      }

      return NextResponse.json(
        {
          error: errorMessage,
          resendError: process.env.NODE_ENV === "development" ? resendError : undefined,
          timestamp: new Date().toISOString()
        },
        { status: statusCode }
      );
    }

    // Vérifier que l'email a bien été envoyé (data.data.id doit exister)
    if (!data.data || !data.data.id) {
      console.error("Resend response missing ID:", data);
      return NextResponse.json(
        {
          error: "L'email n'a pas pu être envoyé. Veuillez réessayer.",
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data }, { status: 200 });
  } catch (error) {
    console.error("Error sending email:", error);

    // Fournir des messages d'erreur plus spécifiques
    let errorMessage = "Échec de l'envoi de l'email";
    let statusCode = 500;

    if (error instanceof Error) {
      // Erreurs spécifiques de Resend
      if (error.message.includes('API key')) {
        errorMessage = "Erreur de configuration du service d'email. Veuillez réessayer plus tard.";
      } else if (error.message.includes('rate limit')) {
        errorMessage = "Trop de tentatives d'envoi. Veuillez patienter quelques minutes avant de réessayer.";
        statusCode = 429;
      } else if (error.message.includes('invalid email')) {
        errorMessage = "Adresse email invalide. Veuillez vérifier votre adresse email.";
        statusCode = 400;
      } else if (error.message.includes('network') || error.message.includes('timeout')) {
        errorMessage = "Problème de connexion. Veuillez vérifier votre connexion internet et réessayer.";
        statusCode = 503;
      } else {
        // Garder le message d'erreur générique pour les autres cas
        errorMessage = "Une erreur temporaire s'est produite lors de l'envoi. Veuillez réessayer dans quelques instants.";
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
        // En développement, inclure plus de détails pour le debug
        ...(process.env.NODE_ENV === "development" && {
          debug: {
            originalError: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          }
        })
      },
      { status: statusCode }
    );
  }
} 
