"use client";
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import {
  GraduationCap,
  Headphones,
  Users,
  Award,
  CheckSquare,
  Video,
  Unlock,
  Clock,
  Link as LinkIcon,
  ChevronRight,
  ChevronDown,
  BookOpen,
  ScrollText,
  UsersRound,
  Palmtree,
  Languages,
  Mic,
  Plus,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { staggerContainer, cardVariants } from "./animation-variants";
import { Button } from "@/components/ui/button";

export function FeaturesGrid() {
  const featuresRef = useRef(null);
  const featuresInView = useInView(featuresRef, { once: true, amount: 0.2 });
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  const iconVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 10,
        delay: 0.2,
      },
    },
    hover: {
      scale: 1.2,
      rotate: [0, -10, 10, -5, 5, 0],
      transition: {
        duration: 0.5,
      },
    },
  };

  const features = [
    {
      icon: <GraduationCap className="h-8 w-8 sm:h-10 sm:w-10" />,
      title: "Formation Complète",
      description:
        "Plus de 120 cours structurés pour progresser à votre rythme, adaptés à tous les niveaux d'apprentissage, incluant 8 heures de coaching personnalisé avec des professeurs natifs.",
      color: "bg-red-100 text-red-600",
      iconPrompt: "Une icône moderne représentant un parcours d'apprentissage complet avec des étapes progressives, style minimaliste et épuré",
      id: "formation-complete"
    },
    {
      icon: <BookOpen className="h-8 w-8 sm:h-10 sm:w-10" />,
      title: "Vocabulaire & Textes",
      description:
        "Enrichissez votre vocabulaire à travers des textes authentiques et des histoires algériennes captivantes qui vous plongent dans la culture locale.",
      color: "bg-green-100 text-green-600",
      iconPrompt: "Une icône représentant un livre ouvert avec des caractères arabes stylisés, symbolisant l'apprentissage du vocabulaire",
      id: "vocabulaire-textes"
    },
    {
      icon: <ScrollText className="h-8 w-8 sm:h-10 sm:w-10" />,
      title: "Conjugaison & Grammaire",
      description:
        "Maîtrisez les règles essentielles de la conjugaison et de la grammaire de l'arabe algérien avec des exercices pratiques et progressifs.",
      color: "bg-blue-100 text-blue-600",
      iconPrompt: "Une icône représentant un parchemin avec des règles de grammaire et des verbes conjugués en arabe",
      id: "conjugaison-grammaire"
    },
    {
      icon: <UsersRound className="h-8 w-8 sm:h-10 sm:w-10" />,
      title: "Situations Réelles",
      description:
        "Apprenez à travers des situations authentiques de la vie quotidienne, des dialogues naturels et des expressions courantes utilisées par les Algériens.",
      color: "bg-purple-100 text-purple-600",
      iconPrompt: "Une icône représentant des personnes en conversation, symbolisant les interactions sociales quotidiennes",
      id: "situations-reelles"
    },
    {
      icon: <Mic className="h-8 w-8 sm:h-10 sm:w-10" />,
      title: "Prononciation & Accent",
      description:
        "Perfectionnez votre prononciation et votre accent avec des exercices audio guidés par des locuteurs natifs, incluant les expressions de douleur et d'émotion.",
      color: "bg-yellow-100 text-yellow-600",
      iconPrompt: "Une icône moderne d'un microphone avec des ondes sonores, symbolisant l'apprentissage de la prononciation",
      id: "prononciation-accent"
    },
    {
      icon: <Plus className="h-8 w-8 sm:h-10 sm:w-10" />,
      title: "Et Bien d'autres...",
      description:
        "Découvrez encore plus de fonctionnalités exclusives : exercices interactifs, quiz personnalisés, communauté d'apprenants, et bien plus encore pour une expérience d'apprentissage complète.",
      color: "bg-orange-100 text-orange-600",
      iconPrompt: "Une icône moderne représentant l'expansion et l'ajout de nouvelles fonctionnalités, style minimaliste",
      id: "et-bien-dautres"
    },
  ];

  const handleCardClick = (index: number) => {
    setExpandedCard(expandedCard === index ? null : index);
  };

  return (
    <section
      id="methode"
      className="w-full py-8 md:py-16 lg:py-20 bg-gradient-to-b from-white to-gray-50"
      ref={featuresRef}
    >
      <div className="container px-4 md:px-6">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={
            featuresInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
          }
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col items-center justify-center gap-2 mb-2 sm:flex-row">
            <h2 className="text-2xl font-bold tracking-tighter md:text-4xl">
              Pourquoi Choisir
            </h2>
            <Image
              src="/images/logo6.svg"
              alt="Parler Algérien Logo"
              width={280}
              height={80}
              className="max-w-[220px] sm:max-w-[280px] md:max-w-[350px]"
              style={{ height: "auto" }}
            />
          </div>
          <p className="text-muted-foreground text-sm md:text-lg max-w-3xl mx-auto px-2">
            Découvrez tous les avantages qui font de notre méthode la plus
            efficace pour apprendre l'algérien
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 items-start"
          variants={staggerContainer}
          initial="hidden"
          animate={featuresInView ? "visible" : "hidden"}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{
                y: -5,
                transition: { duration: 0.3 },
              }}
            >
              <Card 
                className={`h-full border-2 border-gray-100 transition-all duration-300 hover:shadow-xl overflow-hidden cursor-pointer ${expandedCard === index ? "shadow-2xl ring-2 ring-primary/30" : ""}`}
                onClick={() => handleCardClick(index)}
              >
                <CardContent className="p-4 sm:p-6 flex flex-col items-center text-center">
                  <motion.div
                    className={`p-3 sm:p-4 rounded-full mb-3 sm:mb-4 ${feature.color}`}
                    variants={iconVariants}
                    initial="hidden"
                    animate={featuresInView ? "visible" : "hidden"}
                    whileHover="hover"
                  >
                    {feature.icon}
                  </motion.div>
                  <div className="flex items-center justify-center gap-2">
                    <h3 className="text-lg sm:text-xl font-bold">
                      {feature.title}
                    </h3>
                    <motion.div
                      animate={{ rotate: expandedCard === index ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronDown className="h-5 w-5" />
                    </motion.div>
                  </div>
                  <AnimatePresence>
                    {expandedCard === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="text-sm sm:text-base text-muted-foreground mt-4 overflow-hidden"
                      >
                        <p className="mb-2">{feature.description}</p>
                        <Link 
                          href={`/formation#${feature.id}`}
                          className="inline-flex items-center gap-1 text-xs text-muted-foreground hover:text-primary transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          En savoir plus
                          <Plus className="h-3 w-3" />
                        </Link>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
        
        <motion.div 
          className="flex justify-center mt-10 sm:mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={featuresInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Link href="/formation">
            <Button variant="default" className="text-base font-medium transition-transform duration-200 hover:scale-105 hover:brightness-110 px-6 py-6">
              Apprendre Plus
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
