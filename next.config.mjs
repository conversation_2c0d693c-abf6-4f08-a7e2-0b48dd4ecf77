/** @type {import('next').NextConfig} */
const nextConfig = {
  // ===== PERFORMANCE OPTIMIZATIONS =====

  // Compression et minification
  compress: true,

  // Optimisations expérimentales
  experimental: {
    // optimizeCss: true, // Temporairement désactivé pour résoudre l'erreur critters
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // ===== IMAGES OPTIMIZATION =====
  images: {
    unoptimized: false,
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384, 512],
    minimumCacheTTL: 31536000, // 1 an en secondes
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",

    // Domaines externes autorisés pour les images
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'drive.google.com',
        pathname: '/file/d/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
        pathname: '/vi/**',
      },
    ],
  },

  // ===== HEADERS DE SÉCURITÉ ET PERFORMANCE =====
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // Sécurité
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/images/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/(favicon.ico|favicon.png|manifest.json|robots.txt)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400', // 24h
          },
        ],
      },
    ];
  },

  // ===== REDIRECTIONS =====
  async redirects() {
    return [
      // Redirection des anciennes URLs si nécessaire
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/cours',
        destination: '/formation',
        permanent: true,
      },
    ];
  },

  // ===== WEBPACK OPTIMIZATIONS =====
  webpack: (config, { isServer }) => {
    // Support des SVG (seulement côté client)
    if (!isServer) {
      config.module.rules.push({
        test: /\.svg$/,
        use: ['@svgr/webpack'],
      });
    }

    return config;
  },

  // ===== DÉVELOPPEMENT =====
  eslint: {
    // En production, ne pas ignorer les erreurs ESLint
    ignoreDuringBuilds: process.env.NODE_ENV === 'development',
  },
  typescript: {
    // En production, ne pas ignorer les erreurs TypeScript
    ignoreBuildErrors: process.env.NODE_ENV === 'development',
  },

  // ===== AUTRES OPTIMISATIONS =====

  // Optimisation des polyfills
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Configuration des pages statiques
  trailingSlash: false,

  // Optimisation du bundle
  modularizeImports: {
    'lucide-react': {
      transform: 'lucide-react/dist/esm/icons/{{member}}',
    },
  },
}

export default nextConfig
