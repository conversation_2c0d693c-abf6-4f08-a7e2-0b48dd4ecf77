import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16' as any,
});

type ProductWithPrice = {
  id: string;
  name: string;
  price: number;
  currency: string;
  description: string | null;
  interval?: string;
  interval_count?: number;
};

// Associer les produits à leurs IDs Stripe depuis les variables d'environnement
const productIds = {
  'FormuleA': process.env.STRIPE_FORMULEA || '',
  'FormuleB': process.env.STRIPE_FORMULEB || '',
  'FormuleC': process.env.STRIPE_FORMULEC || '',
};

export async function GET(req: Request) {
  try {
    const productsWithPrices: { [key: string]: ProductWithPrice } = {};

    // Vérifier que toutes les variables d'environnement sont définies
    const missingEnvVars = Object.entries(productIds)
      .filter(([_, productId]) => !productId)
      .map(([planName, _]) => planName);

    if (missingEnvVars.length > 0) {
      console.error('Variables d\'environnement manquantes pour:', missingEnvVars);
      return NextResponse.json(
        { error: 'Configuration Stripe incomplète' },
        { status: 500 }
      );
    }

    // Pour chaque produit défini, récupérer ses informations et son prix
    for (const [planName, productId] of Object.entries(productIds)) {
      // Récupérer le produit
      const product = await stripe.products.retrieve(productId);
      
      // Récupérer tous les prix associés à ce produit
      const prices = await stripe.prices.list({ product: productId, active: true });
      
      if (prices.data.length > 0) {
        // Prendre le premier prix actif (généralement, il n'y en a qu'un)
        const price = prices.data[0];
        
        productsWithPrices[planName] = {
          id: product.id,
          name: product.name,
          price: price.unit_amount ? price.unit_amount / 100 : 0,
          currency: price.currency,
          description: product.description,
          ...(price.recurring && {
            interval: price.recurring.interval,
            interval_count: price.recurring.interval_count
          })
        };
      }
    }
    
    return NextResponse.json(productsWithPrices);
  } catch (error) {
    console.error('Stripe products error:', error);
    return NextResponse.json(
      { error: 'Une erreur est survenue lors de la récupération des produits.' },
      { status: 500 }
    );
  }
} 