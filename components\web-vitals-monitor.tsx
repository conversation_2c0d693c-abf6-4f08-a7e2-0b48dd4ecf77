'use client'

import { useEffect } from 'react'
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals'

interface WebVitalsMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
}

export function WebVitalsMonitor() {
  useEffect(() => {
    // Fonction pour envoyer les métriques
    const sendToAnalytics = (metric: WebVitalsMetric) => {
      // Envoyer à Google Analytics 4
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          event_label: metric.id,
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          custom_map: {
            metric_rating: metric.rating,
            metric_delta: metric.delta,
          },
        })
      }

      // Envoyer à Vercel Analytics
      if (typeof window !== 'undefined' && window.va) {
        window.va('event', {
          name: 'Web Vitals',
          data: {
            metric: metric.name,
            value: metric.value,
            rating: metric.rating,
          }
        })
      }

      // Log en développement
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Web Vitals] ${metric.name}:`, {
          value: metric.value,
          rating: metric.rating,
          delta: metric.delta,
        })
      }
    }

    // Mesurer toutes les métriques Core Web Vitals
    onCLS(sendToAnalytics)
    onINP(sendToAnalytics) // INP remplace FID depuis 2024
    onFCP(sendToAnalytics)
    onLCP(sendToAnalytics)
    onTTFB(sendToAnalytics)
  }, [])

  return null // Ce composant ne rend rien
}

// Types pour TypeScript
declare global {
  interface Window {
    gtag?: (...args: any[]) => void
    va?: (event: 'beforeSend' | 'event' | 'pageview', properties?: unknown) => void
  }
}
