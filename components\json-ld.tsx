import { FC } from 'react';

interface JsonLdProps {
  data: Record<string, any>;
}

/**
 * Composant JsonLd pour ajouter des données structurées selon schema.org
 * Améliore la compréhension du contenu par les moteurs de recherche
 */
export const JsonLd: FC<JsonLdProps> = ({ data }) => {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
};

/**
 * Renvoie les données structurées pour le site web (EducationalOrganization)
 */
export const getWebsiteJsonLd = () => {
  return {
    '@context': 'https://schema.org',
    '@type': 'EducationalOrganization',
    name: '<PERSON><PERSON><PERSON>',
    url: 'https://parleralgerien.com',
    logo: 'https://parleralgerien.com/images/logo6.svg',
    description: 'École en ligne spécialisée dans l\'apprentissage de l\'algérien (darija) avec des cours interactifs, des exercices pratiques et des enseignants natifs.',
    alternateName: 'École d\'arabe algérien (darija)',
    foundingDate: '2020',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'FR'
    },
    sameAs: [
      'https://www.facebook.com/parleralgerien/',
      'https://x.com/parleralgerien',
      'https://www.instagram.com/parler_algerien/',
      'https://www.youtube.com/@apprendreaparleralgerien',
      'https://www.tiktok.com/@parleralgerien',
      'https://www.linkedin.com/in/parleralgerien/'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      email: '<EMAIL>',
      contactType: 'customer service',
      availableLanguage: ['fr', 'ar']
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Cours d\'algérien',
      itemListElement: [
        {
          '@type': 'Course',
          name: 'Formation complète en algérien',
          description: 'Apprenez l\'algérien de A à Z avec nos cours structurés',
          provider: {
            '@type': 'EducationalOrganization',
            name: 'Parler Algérien'
          },
          hasCourseInstance: {
            '@type': 'CourseInstance',
            courseMode: 'online',
            instructor: {
              '@type': 'Person',
              name: 'Enseignant natif algérien'
            },
            courseWorkload: 'PT120H'
          },
          offers: {
            '@type': 'Offer',
            category: 'Educational',
            priceCurrency: 'EUR',
            price: '150',
            availability: 'https://schema.org/InStock',
            seller: {
              '@type': 'Organization',
              name: 'Parler Algérien'
            }
          }
        }
      ]
    }
  };
};

/**
 * Renvoie les données structurées pour un cours
 */
export const getCourseJsonLd = (courseName: string, description: string, provider: string = 'Parler Algérien') => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: courseName,
    description: description,
    provider: {
      '@type': 'Organization',
      name: provider,
      sameAs: 'https://parleralgerien.com'
    },
    hasCourseInstance: {
      '@type': 'CourseInstance',
      courseMode: 'online',
      instructor: {
        '@type': 'Person',
        name: 'Enseignant natif algérien',
        description: 'Enseignant natif d\'Oran avec une expérience multiculturelle en Algérie'
      },
      courseWorkload: 'PT120H'
    },
    offers: {
      '@type': 'Offer',
      category: 'Educational',
      priceCurrency: 'EUR',
      price: '150',
      priceValidUntil: '2025-12-31',
      availability: 'https://schema.org/InStock',
      validFrom: '2024-01-01',
      seller: {
        '@type': 'Organization',
        name: provider
      },
      description: 'Formation complète en algérien avec accès en ligne et support pédagogique'
    },
    educationalLevel: 'Beginner to Advanced',
    teaches: 'Algérien (Darija)',
    inLanguage: 'fr',
    coursePrerequisites: 'Aucun prérequis nécessaire',
    timeRequired: 'P6M',
    numberOfCredits: 0,
    educationalCredentialAwarded: 'Certificat de completion'
  };
};

/**
 * Renvoie les données structurées pour une FAQ
 */
export const getFaqJsonLd = (questions: { question: string, answer: string }[]) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: questions.map(q => ({
      '@type': 'Question',
      name: q.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: q.answer
      }
    }))
  };
}; 