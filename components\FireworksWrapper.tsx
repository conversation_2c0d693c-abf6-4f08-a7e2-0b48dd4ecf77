"use client"

import dynamic from 'next/dynamic'
import { useEffect, useState } from 'react'

const Fireworks = dynamic(() => import("./Fireworks"), { ssr: false });

function isFireworksDay() {
  if (typeof window === "undefined") return false;
  const now = new Date();
  const day = now.getDate();
  const month = now.getMonth(); // 0-based: janvier = 0, juillet = 6

  // Affichage du 5 au 15 juillet inclus (toutes les années)
  return day >= 5 && day <= 15 && month === 6;
}

export default function FireworksWrapper() {
  const [showFireworks, setShowFireworks] = useState(false);

  useEffect(() => {
    setShowFireworks(isFireworksDay());
  }, []);

  if (!showFireworks) return null;

  return <Fireworks />;
}
