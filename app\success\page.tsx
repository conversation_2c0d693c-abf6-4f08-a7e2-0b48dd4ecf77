"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { motion } from "framer-motion"
import { Check, Mail, ExternalLink, Home, AlertCircle, Loader2 } from "lucide-react"
import Link from "next/link"
import dynamic from "next/dynamic"

const ReactConfetti = dynamic(() => import('react-confetti'), {
  ssr: false
})

function SuccessPageContent() {
  const searchParams = useSearchParams()
  const sessionId = searchParams.get("session_id")
  const [loading, setLoading] = useState(true)
  const [success, setSuccess] = useState(false)
  const [showConfetti, setShowConfetti] = useState(false)
  const [windowDimensions, setWindowDimensions] = useState({ width: 0, height: 0 })

  useEffect(() => {
    const updateWindowDimensions = () => {
      setWindowDimensions({ width: window.innerWidth, height: window.innerHeight })
    }

    updateWindowDimensions()
    window.addEventListener('resize', updateWindowDimensions)

    return () => window.removeEventListener('resize', updateWindowDimensions)
  }, [])

  useEffect(() => {
    const verifyPayment = async () => {
      if (!sessionId) {
        setLoading(false)
        return
      }
      try {
        // En production, vous devriez vérifier le paiement avec Stripe
        // Ce code est simplifié pour l'exemple
        setSuccess(true)
        setLoading(false)
        setShowConfetti(true)

        // Arrêter les confettis après 5 secondes
        setTimeout(() => setShowConfetti(false), 5000)
      } catch (error) {
        console.error("Erreur lors de la vérification du paiement:", error)
        setSuccess(false)
        setLoading(false)
      }
    }
    verifyPayment()
  }, [sessionId])

  if (loading) {
    return (
      <div className="container flex flex-col items-center justify-center min-h-screen p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <Card className="w-full max-w-md mx-auto">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center space-y-4">
                <Loader2 className="w-12 h-12 text-[#8dc63f] animate-spin" />
                <h2 className="text-xl font-semibold">Vérification du paiement...</h2>
                <p className="text-muted-foreground text-sm">
                  Nous vérifions votre transaction, veuillez patienter.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  if (!success) {
    return (
      <div className="container flex flex-col items-center justify-center min-h-screen p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md mx-auto"
        >
          <Card className="border-red-200 shadow-lg">
            <CardHeader className="text-center">
              <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4">
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>
              <CardTitle className="text-2xl text-red-600">Paiement non validé</CardTitle>
              <CardDescription className="text-base">
                Nous n'avons pas pu confirmer votre paiement. Veuillez réessayer ou contacter notre support.
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col space-y-3">
              <Button asChild className="w-full">
                <Link href="/formules-stripe">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Retour aux formules
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full">
                <Link href="/">
                  <Home className="w-4 h-4 mr-2" />
                  Retour à l'accueil
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="container flex flex-col items-center justify-center min-h-screen p-4">
      {showConfetti && (
        <ReactConfetti
          width={windowDimensions.width}
          height={windowDimensions.height}
          recycle={false}
          numberOfPieces={200}
          colors={['#8dc63f', '#6e9a32', '#ee2c38', '#d01c29', '#ffffff']}
        />
      )}

      <motion.div
        initial={{ opacity: 0, y: 30, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-lg mx-auto"
      >
        <Card className="border-green-200 shadow-xl bg-gradient-to-br from-white to-green-50">
          <CardHeader className="text-center pb-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5, type: "spring", stiffness: 200 }}
              className="w-20 h-20 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center mx-auto mb-6 shadow-lg"
            >
              <Check className="w-10 h-10 text-white" strokeWidth={3} />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <CardTitle className="text-3xl font-bold text-green-700 mb-2">
                Paiement réussi !
              </CardTitle>
              <CardDescription className="text-lg text-gray-600">
                🎉 Félicitations ! Votre inscription a été confirmée avec succès.
              </CardDescription>
            </motion.div>
          </CardHeader>

          <CardContent className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="bg-blue-50 border border-blue-200 rounded-lg p-4"
            >
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-blue-800 mb-1">Prochaines étapes</h3>
                  <p className="text-sm text-blue-700">
                    Vous recevrez très bientôt un email avec vos identifiants de connexion
                    et toutes les informations nécessaires pour commencer votre apprentissage.
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
              className="bg-green-50 border border-green-200 rounded-lg p-4"
            >
              <div className="flex items-start space-x-3">
                <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-green-800 mb-1">Accès immédiat</h3>
                  <p className="text-sm text-green-700">
                    Votre formation est maintenant active ! Commencez dès maintenant votre
                    voyage dans l'apprentissage de l'arabe algérien.
                  </p>
                </div>
              </div>
            </motion.div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-3 pt-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0, duration: 0.5 }}
              className="w-full"
            >
              <Button asChild className="w-full h-12 text-base font-semibold">
                <Link href="https://www.parleralgerien.com/dashboard/fr/login">
                  <ExternalLink className="w-5 h-5 mr-2" />
                  Accéder à mon espace d'apprentissage
                </Link>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.5 }}
              className="w-full"
            >
              <Button variant="outline" asChild className="w-full">
                <Link href="/">
                  <Home className="w-4 h-4 mr-2" />
                  Retour à l'accueil
                </Link>
              </Button>
            </motion.div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}

export default function SuccessPage() {
  return (
    <Suspense fallback={
      <div className="container flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-[#8dc63f] animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </div>
    }>
      <SuccessPageContent />
    </Suspense>
  )
}