/**
 * Configuration centralisée pour les informations de contact
 */

export const CONTACT_CONFIG = {
  // Email principal de contact
  email: process.env.NEXT_PUBLIC_CONTACT_EMAIL || "<EMAIL>",
  
  // Informations pour le formulaire de contact
  form: {
    maxRetries: 2,
    retryDelay: 1000, // en millisecondes
    timeoutDuration: 30000, // 30 secondes
  },
  
  // Messages d'erreur standardisés
  errorMessages: {
    network: "Problème de connexion. Veuillez vérifier votre connexion internet et réessayer." as string,
    timeout: "La requête a pris trop de temps. Veuillez réessayer." as string,
    rateLimit: "Trop de tentatives d'envoi. Veuillez patienter quelques minutes avant de réessayer." as string,
    validation: "Veuillez vérifier les informations saisies." as string,
    recaptcha: "La vérification de sécurité a échoué. Veuillez réessayer." as string,
    server: "Une erreur temporaire s'est produite. Veuillez réessayer dans quelques instants." as string,
    fallback: "Si le problème persiste, vous pouvez nous contacter directement par email." as string,
    // Messages spécifiques à Resend
    resendConfig: "Configuration email en mode test. Veuillez contacter l'administrateur." as string,
    resendDomain: "Domaine email non vérifié. Veuillez contacter l'administrateur." as string,
    resendAuth: "Erreur de configuration du service d'email. Veuillez réessayer plus tard." as string,
  },
  
  // Configuration pour l'email de fallback
  emailFallback: {
    subject: "Contact depuis le site - Parler Algérien",
    getBody: (name: string, email: string, message: string) => 
      `Bonjour,\n\n` +
      `Nom: ${name}\n` +
      `Email: ${email}\n\n` +
      `Message:\n${message}\n\n` +
      `---\n` +
      `Ce message a été envoyé depuis le formulaire de contact du site parleralgerien.com`
  }
} as const;

/**
 * Génère un lien mailto avec les informations du formulaire
 */
export function generateMailtoLink(name: string, email: string, message: string): string {
  const subject = encodeURIComponent(`${CONTACT_CONFIG.emailFallback.subject} - ${name}`);
  const body = encodeURIComponent(CONTACT_CONFIG.emailFallback.getBody(name, email, message));
  
  return `mailto:${CONTACT_CONFIG.email}?subject=${subject}&body=${body}`;
}

/**
 * Détermine le type d'erreur et retourne le message approprié
 */
export function getErrorMessage(error: unknown, retryCount: number = 0): {
  message: string;
  showFallback: boolean;
  canRetry: boolean;
} {
  const { errorMessages, form } = CONTACT_CONFIG;
  let message: string = errorMessages.server;
  let showFallback = retryCount >= form.maxRetries;
  let canRetry = retryCount < form.maxRetries;

  if (error instanceof Error) {
    const errorMsg = error.message.toLowerCase();

    if (errorMsg.includes('network') || errorMsg.includes('fetch')) {
      message = errorMessages.network;
    } else if (errorMsg.includes('timeout')) {
      message = errorMessages.timeout;
    } else if (errorMsg.includes('rate limit') || errorMsg.includes('too many')) {
      message = errorMessages.rateLimit;
      canRetry = false;
      showFallback = true;
    } else if (errorMsg.includes('recaptcha') || errorMsg.includes('captcha')) {
      message = errorMessages.recaptcha;
    } else if (errorMsg.includes('validation') || errorMsg.includes('invalid')) {
      message = errorMessages.validation;
      canRetry = false;
      showFallback = false;
    } else if (errorMsg.includes('testing emails') || errorMsg.includes('own email address')) {
      message = errorMessages.resendConfig;
      canRetry = false;
      showFallback = true;
    } else if (errorMsg.includes('domain') && errorMsg.includes('verify')) {
      message = errorMessages.resendDomain;
      canRetry = false;
      showFallback = true;
    } else if (errorMsg.includes('configuration email') || errorMsg.includes('service d\'email')) {
      message = errorMessages.resendAuth;
      canRetry = false;
      showFallback = true;
    }
  }

  if (showFallback) {
    message += ` ${errorMessages.fallback}`;
  }

  return { message, showFallback, canRetry };
}
