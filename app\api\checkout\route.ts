import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16' as any,
});

// Type pour les configurations de plan
type PlanConfig = {
  priceId: string | undefined;
  productId: string;
  mode: 'payment' | 'subscription';
};

// Type pour la map des plans
type StripePriceMap = {
  [key: string]: PlanConfig;
};

// Associer les plans à leurs IDs Stripe depuis les variables d'environnement
const stripePriceMap: StripePriceMap = {
  'FormuleA': {
    priceId: process.env.STRIPE_FORMULEA_PRICE,
    productId: process.env.STRIPE_FORMULEA || '',
    mode: 'payment' // Ce produit est un paiement unique
  },
  'FormuleB': {
    priceId: process.env.STRIPE_FORMULEB_PRICE,
    productId: process.env.STRIPE_FORMULEB || '',
    mode: 'payment' // Ce produit est un paiement unique
  },
  'FormuleC': {
    priceId: process.env.STRIPE_FORMULEC_PRICE,
    productId: process.env.STRIPE_FORMULEC || '',
    mode: 'payment' // Ce produit est un paiement unique
  },
};

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { plan } = body;

    // Vérifier si le plan est valide
    const planConfig = stripePriceMap[plan];
    if (!planConfig || !planConfig.priceId || !planConfig.productId) {
      console.error(`Configuration manquante pour le plan: ${plan}`, planConfig);
      return NextResponse.json(
        { error: `Plan invalide ou configuration incomplète: ${plan}` },
        { status: 400 }
      );
    }

    // Créer la session de paiement
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: planConfig.priceId,
          quantity: 1,
        },
      ],
      mode: planConfig.mode,
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/#formules`,
    });

    return NextResponse.json({ sessionId: session.id });
  } catch (error) {
    console.error('Stripe checkout error:', error);
    return NextResponse.json(
      { error: 'Une erreur est survenue lors de la création de la session de paiement.' },
      { status: 500 }
    );
  }
} 