/**
 * Script de test pour simuler les erreurs Resend
 * Utilisation : node tests/resend-error-simulation.js
 */

const API_URL = 'http://localhost:3000/api/send-email';

// Données de test
const testData = {
  name: "Test User",
  email: "<EMAIL>",
  message: "Ceci est un message de test pour vérifier la gestion des erreurs Resend.",
  recaptchaToken: "test-token-for-development"
};

/**
 * Fonction pour tester l'API
 */
async function testEmailAPI() {
  console.log('🧪 Test de l\'API d\'envoi d\'email...\n');
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    const data = await response.json();
    
    console.log('📊 Résultats du test :');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (!response.ok) {
      console.log('\n❌ Erreur détectée (comportement attendu si Resend est en mode test)');
      console.log('Message d\'erreur:', data.error);
      
      if (data.resendError) {
        console.log('Erreur Resend originale:', data.resendError);
      }
    } else {
      console.log('\n✅ Email envoyé avec succès');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
}

/**
 * Tests spécifiques pour différents scénarios
 */
async function runSpecificTests() {
  console.log('🎯 Tests spécifiques pour les erreurs Resend\n');
  
  // Test 1: Email normal
  console.log('1️⃣ Test avec email normal...');
  await testEmailAPI();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: Email invalide
  console.log('2️⃣ Test avec email invalide...');
  const invalidEmailData = { ...testData, email: "email-invalide" };
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidEmailData),
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Erreur:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 3: Sans reCAPTCHA
  console.log('3️⃣ Test sans token reCAPTCHA...');
  const noRecaptchaData = { ...testData };
  delete noRecaptchaData.recaptchaToken;
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(noRecaptchaData),
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Erreur:', error.message);
  }
}

/**
 * Affichage des informations de configuration
 */
function displayConfig() {
  console.log('⚙️  Configuration de test :');
  console.log('API URL:', API_URL);
  console.log('Test Data:', JSON.stringify(testData, null, 2));
  console.log('\n' + '='.repeat(50) + '\n');
}

/**
 * Instructions pour les tests manuels
 */
function displayInstructions() {
  console.log('📋 Instructions pour tester les erreurs Resend :\n');
  
  console.log('1. 🔧 Configuration Test Mode :');
  console.log('   - Utilisez une API key Resend de test');
  console.log('   - Configurez RESEND_EMAIL avec un email différent de celui de test');
  console.log('   - Résultat attendu : "Configuration email en mode test"\n');
  
  console.log('2. 🌐 Test Domaine Non Vérifié :');
  console.log('   - Configurez un domaine non vérifié dans Resend');
  console.log('   - Utilisez une adresse "from" avec ce domaine');
  console.log('   - Résultat attendu : "Domaine email non vérifié"\n');
  
  console.log('3. 🔑 Test API Key Invalide :');
  console.log('   - Utilisez une API key incorrecte');
  console.log('   - Résultat attendu : "Erreur de configuration du service d\'email"\n');
  
  console.log('4. ⏱️  Test Rate Limiting :');
  console.log('   - Envoyez plusieurs emails rapidement');
  console.log('   - Résultat attendu : "Limite d\'envoi atteinte"\n');
  
  console.log('5. 📧 Test Email Invalide :');
  console.log('   - Utilisez une adresse email malformée');
  console.log('   - Résultat attendu : "Adresse email invalide"\n');
}

// Exécution du script
async function main() {
  console.log('🚀 Script de Test - Gestion des Erreurs Resend\n');
  
  displayConfig();
  displayInstructions();
  
  // Demander confirmation avant de lancer les tests
  console.log('▶️  Lancement des tests automatiques...\n');
  
  await runSpecificTests();
  
  console.log('\n✅ Tests terminés !');
  console.log('\n💡 Pour tester les erreurs Resend spécifiques, suivez les instructions ci-dessus.');
}

// Lancer le script si exécuté directement
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testEmailAPI, runSpecificTests };
