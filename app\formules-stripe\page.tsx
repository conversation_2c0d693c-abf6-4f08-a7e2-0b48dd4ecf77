import { Metadata } from "next"
import dynamic from 'next/dynamic'

const FormulesSection = dynamic(() => import("@/components/formules-section-stripe").then(mod => ({ default: mod.FormulesSection })), {
  loading: () => <div className="h-[300px] bg-gray-50 flex items-center justify-center"><p>Chargement des formules...</p></div>
})

export const metadata: Metadata = {
  title: "Nos Formules | Parler Algérien",
  description: "Découvrez nos différentes formules d'apprentissage de l'arabe algérien adaptées à vos besoins.",
}

export default function FormulesPage() {
  return (
    <main className="flex-1">
      <FormulesSection />
    </main>
  )
} 