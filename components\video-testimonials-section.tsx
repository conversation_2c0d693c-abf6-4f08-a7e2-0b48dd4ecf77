"use client"

import { motion } from "framer-motion"
import { useInView } from "framer-motion"
import { useRef } from "react"
import { staggerContainer, cardVariants } from "./animation-variants"
import Link from "next/link";
import {
  ChevronRight,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { YouTubeFacade } from "./youtube-facade";

export function VideoTestimonialsSection() {
  const videoRef = useRef(null)
  const videoInView = useInView(videoRef, { once: true, amount: 0.2 })

  const videos = [
    {
      id: "Z3MgECr0r-w",
      type: "youtube",
      title: "Lyubov - Mon étudiante Russe qui visite l'Algérie",
      description: "Lyubov est une étudiante de Moscou qui est venue visiter l'Algérie. ",
      country: "Russie",
      flag: "RU",
      startTime: 0,
    },
    {
      id: "yvan_fiooBA",
      type: "youtube",
      title: "Visite de la ville avec mes étudiants",
      description: 'Mes étudiants "DJAMEL MAZI" et "SOFIANE ZEGHDOUD" qui ont terminé la formation "apprendre à parler algérien" et ont décidé de venir visiter l\'Algérie. Ce fut une belle rencontre !',
      country: "Algérie",
      flag: "BE",
      startTime: 0,
    },
    {
      id: "wLiiznsQf_M",
      type: "youtube",
      title: "Interview avec une étudiante en algérien",
      description: "Myriam est une algérienne française qui, grâce à la formation « apprendre à parler algérien », sait désormais parler algérien.",
      country: "France",
      flag: "FR",
      startTime: 0,
    },
    {
      id: "cdVwb_II0Kk",
      type: "youtube",
      title: "ISSA - Mon étudiant Algéro-Français qui visite l'Algérie",
      description: "Issa un étudiant en darija Algéro-Français qui visite l'ouest algérien pour la première fois ! ",
      country: "France/Algérie",
      flag: "FR",
      startTime: 0,
    },
    {
      id: "IBufvofRLX8",
      type: "youtube",
      title: "Clément - Mon étudiant Français qui visite l'Algérie",
      description: "Clément étudiant en darija, technicien son et artiste de spectacle vous partage son expérience en Algérie. Un français qui est tombé sous le charme de ce pays et de son peuple.",
      country: "France",
      flag: "FR",
      startTime: 0,
    },
    {
      id: "KphKjPM6Zws",
      type: "youtube",
      title: "Sofia - Une Franco-Suisse qui parle algérien",
      description: "De Zurich à Oran, une francophone qui apprend l'Algérien ça donne ça ! Sofia  à la conquête de la langue algérienne renoue avec ses racines algériennes.",
      country: "Suisse",
      flag: "CH",
      startTime: 0,
    }
  ]



  function getFlagUrl(countryCode: string) {
    if (!countryCode) return '';
    return `https://flagcdn.com/${countryCode.toLowerCase()}.svg`;
  }

  return (
    <section id="temoignages-video" className="w-full py-8 md:py-16 lg:py-20 bg-gradient-to-b from-white to-gray-50" ref={videoRef}>
      <div className="container px-4 md:px-6">
        <motion.div
          className="flex flex-col items-center justify-center space-y-4 text-center"
          initial={{ opacity: 0, y: 50 }}
          animate={videoInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.6 }}
        >
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-green-100 px-3 py-1 text-sm text-green-700">
              Témoignages Vidéo
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Nos Étudiants Témoignent</h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl">
              Découvrez les expériences de nos étudiants à travers ces vidéos.
            </p>
          </div>
        </motion.div>

        <motion.div
          className="mx-auto grid max-w-6xl gap-8 py-12 md:grid-cols-2"
          variants={staggerContainer}
          initial="hidden"
          animate={videoInView ? "visible" : "hidden"}
        >
          {videos.map((video, index) => (
            <motion.div key={video.id} variants={cardVariants} className="video-item">
              <YouTubeFacade
                videoId={video.id}
                title={video.title}
                className="aspect-video"
                thumbnailQuality="hqdefault"
              />
              <h3 className="mt-4 text-xl font-bold flex items-center gap-2">
                <img src={getFlagUrl('DZ')} alt="Drapeau Algérie" width="24" />
                {video.title}
                <img src={getFlagUrl(video.flag)} alt={`Drapeau ${video.country}`} width="24" />
              </h3>
              <p className="mt-2 text-muted-foreground">{video.description}</p>
            </motion.div>
          ))}
        </motion.div>
        <motion.div 
          className="flex justify-center mt-10 sm:mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={videoInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Link href="/formules">
            <Button variant="default" className="text-base font-medium px-6 py-6 transition-transform duration-200 hover:scale-105 hover:brightness-110">
              Découvrez plus 
              <ChevronRight className="ml-2 h-3 w-4" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
