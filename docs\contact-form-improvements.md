# 📧 Améliorations du Formulaire de Contact

## 🎯 Objectif
Améliorer la gestion des erreurs dans le formulaire de contact et offrir une alternative par email direct lorsque l'envoi automatique échoue.

## ✨ Nouvelles Fonctionnalités

### 1. **Gestion d'erreurs améliorée**
- Messages d'erreur spécifiques selon le type de problème
- Distinction entre erreurs temporaires et permanentes
- Compteur de tentatives avec limite configurable

### 2. **Email de fallback**
- Bouton "Envoyer par email" après 2 tentatives échouées
- Pré-remplissage automatique du client email
- Lien mailto avec toutes les informations du formulaire

### 3. **Interface utilisateur améliorée**
- Affichage de l'email de contact direct **uniquement en cas d'erreur**
- Boutons d'action contextuels (Réessayer / Envoyer par email)
- Messages d'erreur avec icônes et actions claires

### 4. **Configuration centralisée**
- Fichier `lib/contact-config.ts` pour tous les paramètres
- Messages d'erreur standardisés
- Configuration du timeout et des tentatives

## 🔧 Fichiers Modifiés

### `components/ui/contact-dialog.tsx`
- Ajout de la gestion des tentatives et du fallback email
- Interface utilisateur améliorée pour les erreurs
- Intégration de la configuration centralisée

### `app/api/send-email/route.ts`
- **Gestion complète des erreurs Resend** (test mode, domaine non vérifié, API key, etc.)
- Messages d'erreur plus spécifiques selon le type de problème
- Vérification de la réponse Resend (success/error)
- Gestion des différents types d'erreurs (rate limit, timeout, etc.)
- Informations de debug en mode développement

### `lib/contact-config.ts` (nouveau)
- Configuration centralisée pour le contact
- Fonction de génération de liens mailto
- Logique de détermination des types d'erreurs

### `types/env.d.ts`
- Ajout des variables d'environnement pour email et reCAPTCHA

## 🎨 Types d'Erreurs Gérées

| Type d'erreur | Message | Actions disponibles |
|---------------|---------|-------------------|
| **Réseau** | Problème de connexion | Réessayer + Email |
| **Timeout** | Requête trop longue | Réessayer + Email |
| **Rate Limit** | Trop de tentatives | Email uniquement |
| **reCAPTCHA** | Vérification échouée | Réessayer + Email |
| **Validation** | Données invalides | Corriger les champs |
| **Serveur** | Erreur temporaire | Réessayer + Email |
| **Resend Test** | Configuration en mode test | Email uniquement |
| **Resend Domaine** | Domaine non vérifié | Email uniquement |
| **Resend Auth** | API key invalide | Email uniquement |

## 📋 Configuration

### Variables d'environnement requises
```env
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
RESEND_API_KEY=your_resend_api_key
RESEND_EMAIL=<EMAIL>
RECAPTCHA_SECRET_KEY=your_recaptcha_secret
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_recaptcha_site_key
```

### Paramètres configurables
```typescript
// Dans lib/contact-config.ts
form: {
  maxRetries: 2,           // Nombre max de tentatives
  retryDelay: 1000,        // Délai entre tentatives (ms)
  timeoutDuration: 30000,  // Timeout des requêtes (ms)
}
```

## 🚀 Utilisation

### Scénario normal
1. L'utilisateur remplit le formulaire
2. Le message est envoyé via l'API
3. Redirection vers la page de succès

### Scénario d'erreur
1. L'utilisateur remplit le formulaire
2. L'envoi échoue (1ère tentative)
3. Message d'erreur + bouton "Réessayer"
4. L'envoi échoue à nouveau (2ème tentative)
5. Message d'erreur + boutons "Réessayer" et "Envoyer par email"
6. Le bouton "Envoyer par email" ouvre le client email avec le message pré-rempli

## 🔍 Avantages

- **Meilleure expérience utilisateur** : Messages clairs et actions appropriées
- **Fiabilité** : Alternative garantie même en cas de panne du service
- **Maintenance** : Configuration centralisée et code modulaire
- **Debugging** : Logs détaillés et messages d'erreur spécifiques
- **Accessibilité** : Interface claire avec icônes et textes explicites

## 🧪 Tests Recommandés

1. **Test normal** : Vérifier l'envoi standard
2. **Test timeout** : Simuler une connexion lente
3. **Test rate limit** : Envoyer plusieurs messages rapidement
4. **Test reCAPTCHA** : Désactiver temporairement reCAPTCHA
5. **Test email fallback** : Vérifier le lien mailto généré
6. **Test responsive** : Tester sur mobile et desktop
