import { Metadata } from "next"
import dynamic from 'next/dynamic'

const FormulesSection = dynamic(() => import("@/components/formules-section").then(mod => ({ default: mod.FormulesSection })), {
  loading: () => <div className="h-[300px] bg-gray-50 flex items-center justify-center"><p>Chargement des formules...</p></div>
})

export const metadata: Metadata = {
  title: "Nos Formules d'Apprentissage | Parler Algérien",
  description: "Découvrez nos différentes formules d'apprentissage de l'algérien (darija) adaptées à tous les niveaux et budgets. Formation complète avec enseignants natifs.",
  keywords: ['formules algérien', 'tarifs cours algérien', 'formation darija', 'prix apprentissage', 'cours en ligne algérien'],
  openGraph: {
    title: "Formules d'apprentissage de l'algérien",
    description: "Choisissez la formule qui vous convient pour apprendre l'algérien",
    type: 'website',
  },
}

export default function FormulesPage() {
  return (
    <main className="flex-1">
      <FormulesSection />
    </main>
  )
} 