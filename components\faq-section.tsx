"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { motion } from "framer-motion"
import { useInView } from "framer-motion"
import { useRef, useState } from "react"
import { staggerContainer, cardVariants } from "./animation-variants"
import {
  CalendarPlus,
  UserCheck,
  Clock,
  HelpCircle,
  BookOpen,
  Target,
  CreditCard,
  Timer,
  Map,
  Award,
  Star,
  Heart,
  ChevronDown
} from "lucide-react"
import { cn } from "@/lib/utils"

const faqData = [
  {
    question: "QUAND EST-CE QUE JE POURRAIS DÉMARRER MA FORMATION ?",
    answer: "Quand vous le voulez ! Vous envoyez votre mail, et vous recevez automatiquement les coordonnées de la banque sur votre adresse.",
    icon: CalendarPlus
  },
  {
    question: "EST-CE QUE LE FORMATEUR EST NATIF ?",
    answer: "Le formateur et responsable de la formation est natif d'<PERSON><PERSON>, il a habité dans plusieurs villes en Algérie (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>), il a sa famille à l'est de l'Algérie ainsi qu'au sud. Il propose un dialecte commun afin d'apprendre facilement et rapidement.",
    icon: UserCheck
  },
  {
    question: "JE NE SUIS PAS TRÈS DISPONIBLE, EST CE QUE JE PEUX ME FORMER QUAND MÊME ?",
    answer: "Bien sur ! Vous aurez acces à la formation à n'importe quel moment du mois, de l'année. Vous pouvez avancer à votre rythme et en fonction de votre emploi du temps.",
    icon: Clock
  },
  {
    question: "ET SI J'AI UN PROBLÈME DURANT LA FORMATION ?",
    answer: "Le formateur est joignable sur l'<NAME_EMAIL> pour toute question. Nous ferons le nécessaire pour qu'ensemble, nous puissions vous permettre de suivre votre formation et d'atteindre vos objectifs afin d'apprendre à parler Algérien.",
    icon: HelpCircle
  },
  {
    question: "EXPLIQUEZ MOI LA FORMATION ?",
    answer: "D'abord nous commençerons par les exercices de prononciation, après nous passerons aux pronoms personnels, nous verrons la conjugaison, avec les pronoms posséssifs. Vous aurez des quizz, des textes des audios et des contextes différents. Lorsque vous serez pret(e), vous nous contacter pour commencer les coachings.",
    icon: BookOpen
  },
  {
    question: "EST CE QUE VOUS ME GARANTISSEZ DES RÉSULTATS ?",
    answer: "Nous vous offrons une méthodologie afin que vous puissiez comprendre de façon académique le bon fonctionnement de la langue, vous trouverez la liste de tous les verbes algériens classés. Nous vous proposons d'intégrer des classes virtuelles pour participer à des cours individuels si vous choisissez la formule B.",
    icon: Target
  },
  {
    question: "COMMENT DOIS JE PAYER ?",
    answer: "Vous envoyez votre mail, et vous trouverez les coordonnées de la banque. Vous devez attendre entre 24 et 48 h pour l'envoi des identifiants, une fois le paiment reçu, vous aurez accès à la formation.",
    icon: CreditCard
  },
  {
    question: "POURQUOI ATTENDRE ALORS QUE J'AI DÉJÀ PAYE ?",
    answer: "Nous nous situons en Afrique, notre école en ligne ne nous permet pas d'avoir un compte STRIPE et de recevoir des paiements momentanement. C'est pour cela que vous devez attendre entre 24h et 48h.",
    icon: Timer
  },
  {
    question: "QUEL DIALECTE ? NORD, SUD, EST, OUEST ?",
    answer: "Le formateur propose un dialecte de l'ouest et celui du centre, mais une fois que vous en avez appris un seul, vous serez compris partout !",
    icon: Map
  },
  {
    question: "QUI EST LE FORMATEUR ?",
    answer: "Djamel M'rah est élu prodige algérien en 2012 par le concours DJEZZY prodiges et un ambassadeur de bonne volonté au programme des Nations Unies en 2017 en Chine. Il mène ses recherches sur l'enseignement du dialecte Algérien depuis 2016. Il a commencé à enseigner le dialecte Algérien en 2017.",
    icon: Award
  },
  {
    question: "EST CE QUE C'EST LE SEUL SITE UNIQUE POUR APPRENDRE A PARLER ALGÉRIEN DE MANIÈRE ACADÉMIQUE ?",
    answer: "Nous proposons une méthode déjà testée qui a donné des résultats, mais il faut mentionner aussi que les résultats dépendent de l'implication des étudiants. Notre avantage c'est que les cours sont organisés et de façon méthodique.",
    icon: Star
  },
  {
    question: "MOT DE LA FIN ?",
    answer: "Merci beaucoup pour votre confiance et vos encouragements, mon coeur, mes mots, mes pensées pour vous ! Bienvenue en Algérie et ONE TWO THREE, VIVA L'ALGIRI !",
    icon: Heart
  }
]

export function FAQSection() {
  const faqRef = useRef(null)
  const faqInView = useInView(faqRef, { once: true, amount: 0.1 })
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  return (
    <section id="faq" className="w-full rounded-lg py-6 md:py-12 lg:py-16 bg-muted" ref={faqRef}>
      <div className="container px-4 md:px-6">
        <motion.div
          className="flex flex-col items-center justify-center space-y-4 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-green-100 px-3 py-1 text-sm text-green-700">
              Questions Fréquentes
            </div>
            <h1 className="text-3xl font-bold tracking-tighter md:text-4xl">Questions Fréquentes</h1>
            <p className="max-w-[900px] text-muted-foreground md:text-xl">
              Trouvez les réponses aux questions les plus fréquemment posées.
            </p>
          </div>
        </motion.div>
        <motion.div
          className="mx-auto grid max-w-3xl gap-4 py-12"
          variants={staggerContainer}
          initial="hidden"
          animate={faqInView ? "visible" : "hidden"}
          transition={{ staggerChildren: 0.1 }}
        >
          {faqData.map((faq, index) => {
            const Icon = faq.icon
            return (
              <motion.div
                key={index}
                variants={cardVariants}
                initial={{ opacity: 0, y: 20 }}
                animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="overflow-hidden">
                  <button
                    onClick={() => setOpenIndex(openIndex === index ? null : index)}
                    className="w-full"
                  >
                    <CardHeader className="flex cursor-pointer flex-row items-center justify-between space-y-0 p-4">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full bg-green-100 p-2">
                          <Icon className="h-5 w-5 text-green-700" />
                        </div>
                        <CardTitle className="text-left text-base font-medium">
                          {faq.question}
                        </CardTitle>
                      </div>
                      <ChevronDown
                        className={cn(
                          "h-4 w-4 shrink-0 transition-transform duration-200",
                          openIndex === index ? "rotate-180" : ""
                        )}
                      />
                    </CardHeader>
                  </button>
                  <CardContent
                    className={cn(
                      "grid transition-all duration-200",
                      openIndex === index ? "grid-rows-[1fr] p-4 pt-0" : "grid-rows-[0fr]"
                    )}
                  >
                    <div className="overflow-hidden">
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </motion.div>
      </div>
    </section>
  )
}
