"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Check } from "lucide-react"
import { motion } from "framer-motion"
import { useInView } from "framer-motion"
import { useRef, useState, useEffect } from "react"
import { staggerContainer, cardVariants } from "./animation-variants"
import { redirectToCheckout } from "@/utils/stripe-helpers"
import { toast } from "@/components/ui/use-toast"

// Type pour les informations de prix récupérées depuis l'API
type ProductPriceInfo = {
  id: string;
  name: string;
  price: number;
  currency: string;
  description: string | null;
  interval?: string;
  interval_count?: number;
};

export function FormulesSection() {
  const formulesRef = useRef(null)
  const formulesInView = useInView(formulesRef, { once: true, amount: 0.3 })
  const [isLoading, setIsLoading] = useState<{[key: string]: boolean}>({
    formuleA: false,
    formuleB: false,
    formuleC: false
  })
  // État pour stocker les informations de prix
  const [priceInfo, setPriceInfo] = useState<{[key: string]: ProductPriceInfo | null}>({
    FormuleA: null,
    FormuleB: null,
    FormuleC: null
  })
  const [isLoadingPrices, setIsLoadingPrices] = useState(true)

  // Récupérer les informations de prix au chargement du composant
  useEffect(() => {
    const fetchPrices = async () => {
      try {
        const response = await fetch('/api/plans')
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des prix')
        }
        const data = await response.json()
        setPriceInfo(data)
      } catch (error) {
        console.error('Erreur de récupération des prix:', error)
        toast({
          title: "Erreur",
          description: "Impossible de récupérer les prix. Les prix affichés peuvent ne pas être à jour.",
          variant: "destructive"
        })
      } finally {
        setIsLoadingPrices(false)
      }
    }

    fetchPrices()
  }, [])

  const handleCheckout = async (plan: string) => {
    const loadingKey = plan.toLowerCase().replace('formule', 'formule')
    setIsLoading({...isLoading, [loadingKey]: true})
    try {
      await redirectToCheckout({ plan })
    } catch (error) {
      console.error("Erreur de paiement:", error)
      toast({
        title: "Erreur",
        description: "Un problème est survenu lors du paiement. Veuillez réessayer.",
        variant: "destructive"
      })
    } finally {
      setIsLoading({...isLoading, [loadingKey]: false})
    }
  }

  // Fonction pour formater le prix
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency || 'EUR'
    }).format(price)
  }

  return (
    <section id="formules" className="w-full py-3 md:py-6 lg:py-8 bg-muted" ref={formulesRef}>
      <div className="container px-4 md:px-6">
        <motion.div
          className="flex flex-col items-center justify-center space-y-4 text-center"
          initial={{ opacity: 0, y: 50 }}
          animate={formulesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.6 }}
        >
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-green-100 px-3 py-1 text-sm text-green-700">Nos Formules</div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Choisissez la formule qui vous convient</h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl">
              Des options adaptées à tous les besoins et tous les budgets.
            </p>
          </div>
        </motion.div>
        <motion.div
          className="mx-auto grid max-w-5xl gap-6 py-12 lg:grid-cols-3"
          variants={staggerContainer}
          initial="hidden"
          animate={formulesInView ? "visible" : "hidden"}
        >
          <motion.div variants={cardVariants} whileHover={{ y: -10, transition: { duration: 0.3 } }}>
            <Card className="h-full transition-all duration-300">
              <CardHeader>
                <CardTitle>Accès 6 mois</CardTitle>
                <div className="flex items-baseline gap-1">
                  {isLoadingPrices ? (
                    <span className="text-3xl font-bold">Chargement...</span>
                  ) : (
                    <>
                      <span className="text-3xl font-bold">
                        {priceInfo.FormuleA ? formatPrice(priceInfo.FormuleA.price, priceInfo.FormuleA.currency) : "150€"}
                      </span>
                    </>
                  )}
                </div>
                <CardDescription>Accès à notre formation sur le site  pour une durée de 6 mois.</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Accès au site internet pour 6 mois
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Accès aux 4 premiers chapitres
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Cours qui se débloquent progressivement
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Contenus audio, vidéos, textes
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Exercices et quizz ludiques
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="w-full">
                  <Button
                    className="w-full"
                    variant="outline"
                    disabled={isLoading.formuleA || isLoadingPrices}
                    onClick={() => handleCheckout("FormuleA")}
                  >
                    {isLoading.formuleA ? "Traitement..." : "Commencer"}
                  </Button>
                </motion.div>
              </CardFooter>
            </Card>
          </motion.div>
          <motion.div
            variants={cardVariants}
            whileHover={{ y: -10, transition: { duration: 0.3 } }}
            className="lg:scale-105 lg:-translate-y-2"
          >
            <Card className="border-green-500 shadow-lg h-full transition-all duration-300">
              <CardHeader>
                <motion.div
                  className="inline-block rounded-full bg-green-100 px-3 py-1 text-xs text-green-700 mb-2"
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatType: "reverse",
                  }}
                >
                  Plus Populaire
                </motion.div>
                <CardTitle>Accès 1 an</CardTitle>
                <div className="flex items-baseline gap-1">
                  {isLoadingPrices ? (
                    <span className="text-3xl font-bold">Chargement...</span>
                  ) : (
                    <>
                      <span className="text-3xl font-bold">
                        {priceInfo.FormuleB ? formatPrice(priceInfo.FormuleB.price, priceInfo.FormuleB.currency) : "229€"}
                      </span>
                    </>
                  )}
                </div>
                <CardDescription>Accès annuel à notre formation sur le site avec plus de 80 cours répartis en 12 chapitres complets.</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Accès au site internet pour 1 an
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Plus de 80 cours
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    12 chapitres complets
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Apprentissage à votre rythme
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Comprendre et parler algérien
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Contenus audio, vidéos, textes, exercices
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="w-full">
                  <Button
                    className="w-full"
                    variant="default"
                    disabled={isLoading.formuleB || isLoadingPrices}
                    onClick={() => handleCheckout("FormuleB")}
                  >
                    {isLoading.formuleB ? "Traitement..." : "S'abonner"}
                  </Button>
                </motion.div>
              </CardFooter>
            </Card>
          </motion.div>
          <motion.div variants={cardVariants} whileHover={{ y: -10, transition: { duration: 0.3 } }}>
            <Card className="h-full transition-all duration-300">
              <CardHeader>
                <CardTitle>Accès 1 an + Cours particuliers</CardTitle>
                <div className="flex items-baseline gap-1">
                  {isLoadingPrices ? (
                    <span className="text-3xl font-bold">Chargement...</span>
                  ) : (
                    <>
                      <span className="text-3xl font-bold">
                        {priceInfo.FormuleC ? formatPrice(priceInfo.FormuleC.price, priceInfo.FormuleC.currency) : "480€"}
                      </span>
                    </>
                  )}
                </div>
                <CardDescription>Accès annuel au site internet + 10 heures de cours particuliers en visioconférence pour un apprentissage personnalisé et une mise en pratique optimale.</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Accès au site internet pour 1 an
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Plus de 80 cours et 12 chapitres complets
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    10 heures de cours individuels en visioconférence
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Mise en pratique personnalisée
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Accompagnement individuel
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Amélioration compréhension et prononciation
                  </li>
                  <li className="flex items-center">
                    <Check className="mr-2 h-4 w-4 text-green-500" />
                    Conseils personnalisés
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="w-full">
                  <Button
                    className="w-full"
                    variant="secondary"
                    disabled={isLoading.formuleC || isLoadingPrices}
                    onClick={() => handleCheckout("FormuleC")}
                  >
                    {isLoading.formuleC ? "Traitement..." : "Commencer Premium"}
                  </Button>
                </motion.div>
              </CardFooter>
            </Card>
          </motion.div>
        </motion.div>
        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={formulesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <p className="text-sm text-muted-foreground">
            <strong>Cours supplémentaires :</strong> Vous pouvez à tout moment demander des cours individuels supplémentaires en visioconférence à 150€ pour 5 heures de cours.
          </p>
        </motion.div>
      </div>
    </section>
  )
}
