#!/usr/bin/env node

/**
 * Script de validation des données structurées
 * Vérifie que les schémas Course ont tous les champs requis
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validation des données structurées - <PERSON><PERSON><PERSON>\n');

// Fonction pour valider un schéma Course
function validateCourseSchema(courseData, source) {
  const errors = [];
  
  if (!courseData.hasCourseInstance) {
    errors.push('❌ Champ manquant: hasCourseInstance');
  }
  
  if (!courseData.offers) {
    errors.push('❌ Champ manquant: offers');
  }
  
  if (!courseData.name) {
    errors.push('❌ Champ manquant: name');
  }
  
  if (!courseData.description) {
    errors.push('❌ Champ manquant: description');
  }
  
  if (!courseData.provider) {
    errors.push('❌ Champ manquant: provider');
  }
  
  if (errors.length === 0) {
    console.log(`✅ ${source}: Schéma Course valide`);
    
    // Vérifications détaillées
    if (courseData.hasCourseInstance) {
      console.log(`   ✓ hasCourseInstance: ${courseData.hasCourseInstance['@type']}`);
    }
    
    if (courseData.offers) {
      console.log(`   ✓ offers: ${courseData.offers['@type']} - ${courseData.offers.priceCurrency} ${courseData.offers.price}`);
    }
    
    return true;
  } else {
    console.log(`❌ ${source}: Erreurs détectées:`);
    errors.forEach(error => console.log(`   ${error}`));
    return false;
  }
}

// 1. Tester le composant json-ld.tsx
const jsonLdPath = path.join(__dirname, '../components/json-ld.tsx');
if (fs.existsSync(jsonLdPath)) {
  console.log('📋 Vérification du fichier json-ld.tsx...\n');
  
  const content = fs.readFileSync(jsonLdPath, 'utf8');
  
  // Vérifier que les fonctions contiennent les champs requis
  if (content.includes('hasCourseInstance') && content.includes('offers')) {
    console.log('✅ Fonctions getCourseJsonLd et getWebsiteJsonLd mises à jour');
  } else {
    console.log('❌ Champs manquants dans les fonctions');
  }
}

// 2. Simuler les données structurées
console.log('\n📊 Test des données structurées générées...\n');

// Simuler getCourseJsonLd
const mockCourseData = {
  '@context': 'https://schema.org',
  '@type': 'Course',
  name: 'Formation complète en algérien',
  description: 'Test description',
  provider: {
    '@type': 'Organization',
    name: 'Parler Algérien',
    sameAs: 'https://parleralgerien.com'
  },
  hasCourseInstance: {
    '@type': 'CourseInstance',
    courseMode: 'online',
    instructor: {
      '@type': 'Person',
      name: 'Enseignant natif algérien'
    }
  },
  offers: {
    '@type': 'Offer',
    category: 'Educational',
    priceCurrency: 'EUR',
    price: '150',
    availability: 'https://schema.org/InStock'
  }
};

validateCourseSchema(mockCourseData, 'getCourseJsonLd()');

// Simuler le cours dans getWebsiteJsonLd
const mockWebsiteCourse = {
  '@type': 'Course',
  name: 'Formation complète en algérien',
  description: 'Apprenez l\'algérien de A à Z avec nos cours structurés',
  provider: {
    '@type': 'EducationalOrganization',
    name: 'Parler Algérien'
  },
  hasCourseInstance: {
    '@type': 'CourseInstance',
    courseMode: 'online',
    instructor: {
      '@type': 'Person',
      name: 'Enseignant natif algérien'
    }
  },
  offers: {
    '@type': 'Offer',
    category: 'Educational',
    priceCurrency: 'EUR',
    price: '150',
    availability: 'https://schema.org/InStock'
  }
};

validateCourseSchema(mockWebsiteCourse, 'getWebsiteJsonLd() - Course dans catalog');

console.log('\n🎯 Résumé de la correction:');
console.log('   • Ajout du champ hasCourseInstance avec CourseInstance');
console.log('   • Ajout du champ offers avec informations de prix');
console.log('   • Enrichissement avec des métadonnées éducatives');
console.log('   • Conformité avec schema.org Course');

console.log('\n📈 Impact attendu:');
console.log('   • Résolution des 6 erreurs de données structurées');
console.log('   • Amélioration du référencement des cours');
console.log('   • Meilleure compréhension par Google');
