import { useEffect, useRef, useState } from "react";
import { Fireworks } from "@fireworks-js/react";

const COLORS = ["#218838", "#ffffff", "#e10600"];

export default function FireworksComponent() {
  const [active, setActive] = useState(true);
  const [showMessage, setShowMessage] = useState(false);
  const [hideMessage, setHideMessage] = useState(false);
  const [showHappy, setShowHappy] = useState(false);
  const [showIndependence, setShowIndependence] = useState(false);
  const [showDay, setShowDay] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fwTimeout = setTimeout(() => {
      setActive(false);
      setShowMessage(true);
    }, 10000);
    let msgTimeout: NodeJS.Timeout;
    if (showMessage) {
      setShowHappy(false);
      setShowIndependence(false);
      setShowDay(false);
      setTimeout(() => setShowHappy(true), 100);
      setTimeout(() => setShowIndependence(true), 1500);
      setTimeout(() => setShowDay(true), 3000);
      msgTimeout = setTimeout(() => setHideMessage(true), 8000);
    }
    return () => {
      clearTimeout(fwTimeout);
      if (msgTimeout) clearTimeout(msgTimeout);
    };
  }, [showMessage]);

  const options = {
    hue: { min: 0, max: 360 },
    delay: { min: 30, max: 80 },
    speed: 2.2,
    acceleration: 1.00,
    friction: 0.95,
    gravity: 1.4,
    particles: 60,
    traceLength: 3,
    traceSpeed: 10,
    explosion: 5,
    intensity: 30.0,
    autoresize: true,
    brightness: { min: 50, max: 90 },
    decay: { min: 0.015, max: 0.030 },
    mouse: { click: false, move: false, max: 0 },
    boundaries: {
      x: 50,
      y: 50,
      width: typeof window !== 'undefined' ? window.innerWidth - 100 : 300,
      height: typeof window !== 'undefined' ? window.innerHeight - 100 : 300,
    },
    lineWidth: {
      explosion: { min: 1, max: 4 },
      trace: { min: 0.1, max: 1 },
    },
  };
  const shadowHappy = "0 0 32px #218838, 0 0 16px #218838, 0 2px 32px #218838, 0 0 8px #218838, 0 0 2px #218838";
  const shadowIndependence = "0 0 32px #218838, 0 0 16px #fff, 0 2px 32px #fff, 0 0 8px #e10600, 0 0 2px #e10600";
  const shadowDay = "0 0 32px #e10600, 0 0 16px #e10600, 0 2px 32px #e10600, 0 0 8px #e10600, 0 0 2px #e10600";

  // Dégradés festifs pour chaque mot
  const happyStyle = {
    color: "#fff",
    background: "linear-gradient(90deg, #e10600 0%, #fff 50%, #218838 100%)",
    WebkitBackgroundClip: "text",
    textAlign: "center" as const,
    fontWeight: 900,
    textShadow: shadowHappy,
    opacity: showHappy ? 1 : 0,
    transition: "opacity 0.6s cubic-bezier(.4,2,.6,1)",
  };
  const independenceStyle = {
    color: "#fff",
    background: "linear-gradient(90deg, #fff 0%, #e10600 50%, #218838 100%)",
    WebkitBackgroundClip: "text",
    textAlign: "center" as const,
    fontWeight: 900,
    textShadow: shadowIndependence,
    opacity: showIndependence ? 1 : 0,
    transition: "opacity 0.6s cubic-bezier(.4,2,.6,1)",
  };
  const dayStyle = {
    color: "#fff",
    background: "linear-gradient(90deg, #218838 0%, #fff 50%, #e10600 100%)",
    WebkitBackgroundClip: "text",
    textAlign: "center" as const,
    fontWeight: 900,
    textShadow: shadowDay,
    opacity: showDay ? 1 : 0,
    transition: "opacity 0.6s cubic-bezier(.4,2,.6,1)",
  };

  if (hideMessage) return null;

  return (
    <div
      ref={containerRef}
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: "100vw",
        height: "100vh",
        pointerEvents: "none",
        zIndex: 9999,
        transform: "translateZ(0)",
        willChange: "transform",
      }}
    >
      {active && (
        <>
          <Fireworks
            options={{ ...options, rocketsPoint: { min: 0.1, max: 0.2 } }}
            style={{ width: "100vw", height: "100vh", position: "fixed", top: 0, left: 0 }}
          />
          <Fireworks
            options={{ ...options, rocketsPoint: { min: 0.8, max: 0.9 } }}
            style={{ width: "100vw", height: "100vh", position: "fixed", top: 0, left: 0 }}
          />
        </>
      )}
      {showMessage && !hideMessage && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            pointerEvents: "none",
            zIndex: 10000,
            background: "rgba(0,0,0,0.2)",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              transform: "rotate(-15deg) skew(-10deg, -5deg)",
              filter: "drop-shadow(0 0 16px #fff8) drop-shadow(0 0 8px #e10600aa)",
              gap: "0.2em",
            }}
          >
            <span style={{ ...happyStyle, fontSize: "clamp(2rem, 6vw, 5rem)", lineHeight: 1 }}>يوم استقلال سعيد</span>
            <span style={{ ...independenceStyle, fontSize: "clamp(2rem, 5vw, 4rem)", lineHeight: 1 }}>Joyeuse fête de l'independance</span>
            <span style={{ ...dayStyle, fontSize: "clamp(2rem, 6vw, 6rem)", lineHeight: 1 }}>Happy Independence Day</span>
            <span style={{ display: "flex", flexDirection: "row", alignItems: "center", gap: "0.2em" }}>
              <span style={{ ...happyStyle, fontSize: "clamp(2rem, 6vw, 6rem)", lineHeight: 1 }}>05</span>
              <span style={{ ...independenceStyle, fontSize: "clamp(2rem, 6vw, 6rem)", lineHeight: 1 }}>Juillet</span>
              <span style={{ ...dayStyle, fontSize: "clamp(2rem, 6vw, 6rem)", lineHeight: 1 }}>2025</span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
} 