"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { Facebook, Instagram, Youtube, Twitter, Linkedin } from "lucide-react";

export function Footer() {
  return (
    <motion.footer
      className="w-full  text-black-100 border-t border-gray-300 relative"
      style={{
        backgroundImage: 'url("/images/footerback4.avif")',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
      initial={{ opacity: 0, y: 50 }}  
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8, delay: 1 }}
    >
      <div
        className="pointer-events-none absolute inset-0 z-0"
        style={{
          background:
            "linear-gradient(90deg, rgba(0,0,0,0.75) 0%, rgba(0,0,0,0.15) 20%, rgba(0,0,0,0.15) 80%, rgba(0,0,0,0.75) 100%)",
          backdropFilter: 'blur(2px)',
          WebkitBackdropFilter: 'blur(2px)',
        }}
      />
      <div className="relative z-10 bg-white/20 pt-8 pb-2 px-4 ">
        <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-4">
          <div className="space-y-4">
            <motion.div
              className="flex items-center gap-2"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Image
                src="/images/logofooter2.svg"
                alt="Parler Algerien Logo"
                width={200}
                height={80}
                className="img-fluid"
              />
            </motion.div>
            <p className="text-sm">
              Apprenez l'algérien avec une méthode efficace et structurée.
              Rejoignez notre communauté d'apprenants passionnés du monde
              entier.
            </p>
          </div>
          <div className="space-y-4">
            <h3 className="text-lg font-bold pb-2 border-b-4 border-[#6e9a32] text-[#6e9a32] drop-shadow-hero">
              Contactez-Nous
            </h3>
            <Link href="/formules" className="text-sm flex items-center gap-2 hover:text-[#6e9a32] transition-colors">
              
              Nous contactez
            </Link>
            <div className="flex gap-4 mt-4">
              <motion.div whileHover={{ scale: 1.2, rotate: 5 }}>
                <Link
                  href="https://www.facebook.com/parleralgerien/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className=" hover:text-blue-500"
                >
                  <Facebook size={24} />
                  <span className="sr-only">Facebook</span>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2, rotate: 5 }}>
                <Link
                  href="https://www.instagram.com/parler_algerien/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className=" hover:text-purple-500"
                >
                  <Instagram size={24} />
                  <span className="sr-only">Instagram</span>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2, rotate: 5 }}>
                <Link
                  href="https://x.com/parleralgerien"
                  target="_blank"
                  rel="noopener noreferrer"
                  className=" hover:text-blue-400"
                >
                  <Twitter size={24} />
                  <span className="sr-only">Twitter/X</span>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2, rotate: 5 }}>
                <Link
                  href="https://www.linkedin.com/in/parleralgerien/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className=" hover:text-blue-600"
                >
                  <Linkedin size={24} />
                  <span className="sr-only">LinkedIn</span>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2, rotate: 5 }}>
                <Link
                  href="https://www.youtube.com/@apprendreaparleralgerien"
                  target="_blank"
                  rel="noopener noreferrer"
                  className=" hover:text-red-500"
                >
                  <Youtube size={24} />
                  <span className="sr-only">Youtube</span>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2, rotate: 5 }}>
                <Link
                  href="https://www.tiktok.com/@parleralgerien"
                  target="_blank"
                  rel="noopener noreferrer"
                  className=" hover:text-gray-500"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 18 18"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6"
                  >
                    <path d="M9 0h1.98c.144.715.54 1.617 1.235 2.512C12.895 3.389 13.797 4 15 4v2c-1.753 0-3.07-.814-4-1.829V11a5 5 0 1 1-5-5v2a3 3 0 1 0 3 3V0Z" />
                  </svg>
                  <span className="sr-only">Tiktok</span>
                </Link>
              </motion.div>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-lg font-bold pb-2 border-b-4 border-[#6e9a32] text-[#6e9a32] drop-shadow-hero">
              Formation Disponible
            </h3>
            <p className="text-sm flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-globe"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M2 12h20" />
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
              </svg>
              24h/24 - 7j/7
            </p>
            <p className="text-sm flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-users"
              >
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="m22 21-2-2" />
                <path d="M16 16h6" />
              </svg>
              Communauté mondiale
            </p>
            <p className="text-sm flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-zap"
              >
                <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2" />
              </svg>
              Apprentissage flexible
            </p>
          </div>
          <div className="space-y-4">
            <h3 className="text-lg font-bold pb-2 border-b-4 border-[#6e9a32] text-[#6e9a32] drop-shadow-hero">
              Lieu
            </h3>
            <p className="text-sm  flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-map-pin"
              >
                <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                <circle cx="12" cy="10" r="3" />
              </svg>
              Bientöt notre nouvelle addresse
            </p>
          </div>
        </div>
        <motion.div
          className="mt-12 border-t pt-2 text-center text-sm "
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <p >© 2020 - 2025 Parler Algerien. Tous droits réservés.</p>
        </motion.div>
      </div>
    </motion.footer>
  );
}
