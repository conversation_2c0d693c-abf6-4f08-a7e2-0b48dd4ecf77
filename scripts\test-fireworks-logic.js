#!/usr/bin/env node

/**
 * Script de test pour vérifier la logique des feux d'artifice
 * Teste différentes dates pour s'assurer que les feux d'artifice
 * ne s'affichent que du 5 au 15 juillet inclus
 */

// Simulation de la fonction isFireworksDay
function isFireworksDay(testDate) {
  const now = testDate || new Date();
  const day = now.getDate();
  const month = now.getMonth(); // 0-based: janvier = 0, juillet = 6
  
  // Affichage du 5 au 15 juillet inclus (toutes les années)
  return day >= 5 && day <= 15 && month === 6;
}

console.log('🎆 Test de la logique des feux d\'artifice\n');

// Dates de test
const testDates = [
  // Avant la période
  { date: new Date(2024, 6, 4), expected: false, label: '4 juillet 2024' },
  
  // Début de la période
  { date: new Date(2024, 6, 5), expected: true, label: '5 juillet 2024' },
  { date: new Date(2025, 6, 5), expected: true, label: '5 juillet 2025' },
  
  // Milieu de la période
  { date: new Date(2024, 6, 10), expected: true, label: '10 juillet 2024' },
  { date: new Date(2026, 6, 12), expected: true, label: '12 juillet 2026' },
  
  // Fin de la période
  { date: new Date(2024, 6, 15), expected: true, label: '15 juillet 2024' },
  { date: new Date(2025, 6, 15), expected: true, label: '15 juillet 2025' },
  
  // Après la période
  { date: new Date(2024, 6, 16), expected: false, label: '16 juillet 2024' },
  { date: new Date(2024, 6, 20), expected: false, label: '20 juillet 2024' },
  
  // Autres mois
  { date: new Date(2024, 5, 10), expected: false, label: '10 juin 2024' },
  { date: new Date(2024, 7, 10), expected: false, label: '10 août 2024' },
  { date: new Date(2024, 11, 10), expected: false, label: '10 décembre 2024' },
];

let allTestsPassed = true;

testDates.forEach(({ date, expected, label }) => {
  const result = isFireworksDay(date);
  const status = result === expected ? '✅' : '❌';
  const emoji = result ? '🎆' : '🚫';
  
  console.log(`${status} ${label}: ${emoji} ${result ? 'AFFICHAGE' : 'MASQUÉ'}`);
  
  if (result !== expected) {
    allTestsPassed = false;
    console.log(`   ⚠️  Attendu: ${expected}, Obtenu: ${result}`);
  }
});

console.log('\n📊 Résumé:');
if (allTestsPassed) {
  console.log('✅ Tous les tests sont passés !');
  console.log('🎯 Les feux d\'artifice s\'afficheront uniquement du 5 au 15 juillet');
  console.log('💡 Le composant Fireworks ne sera PAS chargé en dehors de cette période');
} else {
  console.log('❌ Certains tests ont échoué');
}

console.log('\n🔧 Logique implémentée:');
console.log('   • Période: 5-15 juillet (inclus)');
console.log('   • Toutes les années supportées');
console.log('   • Composant non chargé en dehors de la période');
