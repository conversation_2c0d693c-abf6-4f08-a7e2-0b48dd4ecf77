# Parler Algérien - Plateforme d'Apprentissage de la Langue Algérienne

## 📋 Table des matières
- [Description du Projet](#description-du-projet)
- [Technologies Utilisées](#technologies-utilisées)
- [Structure du Projet](#structure-du-projet)
- [Composants Principaux](#composants-principaux)
- [Fonctionnalités](#fonctionnalités)
- [Installation](#installation)
- [Développement](#développement)
- [Déploiement](#déploiement)

## Description du Projet
Parler Algérien est une plateforme web moderne dédiée à l'apprentissage de la langue algérienne. Le projet vise à offrir une expérience d'apprentissage interactive et engageante à travers des leçons structurées, des exercices pratiques et une communauté d'apprenants.

## Technologies Utilisées
- **Next.js 14** - Framework React pour le rendu côté serveur et la génération statique
- **TypeScript** - Typage statique pour une meilleure maintenabilité
- **Tailwind CSS** - Framework CSS utilitaire
- **Framer Motion** - Bibliothèque d'animation
- **Lucide Icons** - Bibliothèque d'icônes
- **pnpm** - Gestionnaire de paquets

## Structure du Projet
```
parleralgerien/
├── app/                    # Pages de l'application
├── components/            # Composants React
│   ├── ui/               # Composants UI réutilisables
│   └── *.tsx            # Composants de page
├── lib/                  # Utilitaires et configurations
├── public/              # Fichiers statiques
├── styles/             # Styles globaux
└── hooks/             # Hooks personnalisés
```

## Composants Principaux

### 1. CommunitySection
Gère l'affichage des statistiques de la communauté avec des animations de compteur.
- **Fonctionnalités** :
  - Animations de compteur avec easing
  - Détection responsive
  - Transitions fluides
  - Gestion des états complexes

### 2. TestimonialsCarousel
Affiche les témoignages des apprenants dans un carousel interactif.
- **Fonctionnalités** :
  - Défilement automatique
  - Navigation manuelle
  - Transitions fluides
  - Optimisation des performances

### 3. FormulesSection
Présente les différentes formules d'apprentissage disponibles.
- **Fonctionnalités** :
  - Animations de blob
  - Effets de verre dépoli
  - Transitions séquentielles
  - Design responsive

## Fonctionnalités
- 🎯 Interface utilisateur moderne et responsive
- 🎨 Animations fluides et interactives
- 📱 Support mobile optimisé
- 🌐 Internationalisation (i18n)
- 🔍 Optimisation SEO
- ⚡ Performance optimisée

## Installation

1. **Prérequis**
   - Node.js 18+
   - pnpm

2. **Installation des dépendances**
   ```bash
   pnpm install
   ```

3. **Configuration**
   - Copier `.env.example` vers `.env`
   - Configurer les variables d'environnement

## Développement

1. **Démarrer le serveur de développement**
   ```bash
   pnpm dev
   ```

2. **Linting**
   ```bash
   pnpm lint
   ```

3. **Tests**
   ```bash
   pnpm test
   ```

## Déploiement

Le projet est configuré pour être déployé sur Vercel :

1. **Build de production**
   ```bash
   pnpm build
   ```

2. **Vérification du build**
   ```bash
   pnpm start
   ```

## Contribution
Les contributions sont les bienvenues ! Veuillez suivre ces étapes :
1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## Licence
Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## Contact
Pour toute question ou suggestion, n'hésitez pas à nous contacter via :
- Email : <EMAIL>
- Site web : https://parleralgerien.com 