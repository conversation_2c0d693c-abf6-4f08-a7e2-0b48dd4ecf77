"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { useState } from "react"
import { Linkedin, Youtube, Facebook, Instagram } from "lucide-react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose
} from "@/components/ui/dialog"

// Teacher interface type
interface Teacher {
  id: number
  name: string
  role: string
  bio: string
  image: string
  longBio: string
  expertise: string[]
  achievements: string[]
  languages: string[]
  social?: {
    linkedin?: string
    youtube?: string
    facebook?: string
    instagram?: string
  }
}

// Données des enseignants
const teachers: Teacher[] = [
  {
    "id": 2,
    "name": "<PERSON><PERSON><PERSON>rah",
    "role": "Instructeur senior / Fondateur ParlerAlgerien.com",
    "bio": "Enseignant, musicien, écrivain et entrepreneur algérien issu d'une lignée engagée dans la culture et l'éducation.",
    "image": "/images/mrah.avif",
    "longBio": "Je suis D<PERSON>mel <PERSON>, enseignant passionné et fondateur de la plateforme Parler Al<PERSON>. Depuis 2016, j’ai accompagné des centaines de francophones dans l’apprentissage du dialecte algérien, en m’appuyant sur une méthode claire, humaine et ancrée dans la culture populaire. Créateur de la chaîne YouTube Parler Algérien, devenue une référence pour celles et ceux qui souhaitent découvrir la langue et l’âme de l’Algérie, je transmets bien plus que des mots : une manière de penser, de ressentir et de se connecter aux autres. Grâce à ma maîtrise de plusieurs langues et à une approche pédagogique fondée sur la simplicité et l’écoute, j’aide chacun à progresser avec confiance. Apprendre avec moi, c’est vivre une expérience authentique, sans pression inutile, mais avec un cadre structuré et un accompagnement bienveillant. Rejoignez-moi et avançons ensemble vers votre réussite en darija !",
    "expertise": ["Langue algérienne", "Littérature", "Audiovisuel", "Communication interculturelle"],
    "achievements": ["Lauréat Djezzy Prodige 2012", "Participant au programme D.A.R.C", "Sélectionné par le PNUD pour les ODD", "Création de contenus culturels algériens"],
    "languages": ["Arabe (natif)", "Français", "Anglais" , "Russe"],
    "social": {
      "linkedin": "https://www.linkedin.com/in/parleralgerien/",
      "youtube": "https://www.youtube.com/@apprendreaparleralgerien",
      "facebook": "https://www.facebook.com/parleralgerien/",
      "instagram": "https://www.instagram.com/parler_algerien/"
    }
  },
   
]

export function TeachersSection() {
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);
  
  return (
    <section className="w-full rounded-t-lg py-3 md:py-6 lg:py-8 bg-muted/80">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl">Votre Enseignant Natif</h1>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed mt-4">
              Découvrez votre enseignant natif algérien, expert en pédagogie et passionné par la transmission de sa langue maternelle. Avec des années d'expérience dans l'enseignement de l'algérien (darija), il vous accompagne personnellement dans votre parcours d'apprentissage.
            </p>
          </motion.div>
          
          <div className="flex justify-center mt-8">
            {teachers.map((teacher, index) => (
              <motion.div
                key={teacher.id}
                className={`flex flex-col items-center space-y-4 rounded-lg border p-6 ${
                  teacher.id === 2 
                    ? "bg-gradient-to-br from-background to-green-50 shadow-[0_0_15px_rgba(34,197,94,0.3)] border-green-200 md:scale-110 md:-mt-4 md:mb-4 z-10 hover:shadow-[0_0_25px_rgba(34,197,94,0.5)]" 
                    : "bg-background shadow-sm"
                }`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5, scale: teacher.id === 2 ? 1.12 : 1 }}
              >
                <div className={`relative overflow-hidden rounded-full ${
                  teacher.id === 2 ? "h-48 w-48" : "h-40 w-40"
                }`}>
                  <Image
                    src={teacher.image}
                    alt={teacher.name}
                    fill
                    className="object-cover"
                  />                  
                </div>
                <div className="space-y-2 text-center">
                  <h3 className={`${teacher.id === 2 ? "text-2xl text-green-700" : "text-xl"} font-bold`}>{teacher.name}</h3>
                  <p className={`${teacher.id === 2 ? "text-base" : "text-sm"} text-green-600 font-medium`}>{teacher.role}</p>
                  <p className={`${teacher.id === 2 ? "text-base" : "text-sm"} text-muted-foreground`}>{teacher.bio}</p>
                </div>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => setSelectedTeacher(teacher)}
                >
                  Voir le profil
                </Button>
              </motion.div>
            ))}
          </div>
          
          <Dialog open={!!selectedTeacher} onOpenChange={(open) => !open && setSelectedTeacher(null)}>
            <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
              {selectedTeacher && (
                <>
                  <DialogHeader>
                    <DialogTitle className="text-2xl">{selectedTeacher.name}</DialogTitle>
                    <DialogDescription className="text-green-600 font-medium">{selectedTeacher.role}</DialogDescription>
                  </DialogHeader>
                  
                  <div className="flex flex-col md:flex-row gap-6 py-4">
                    <div className="flex-shrink-0">
                      <div className="relative h-36 w-36 md:h-48 md:w-48 overflow-hidden rounded-full mx-auto">
                        <Image
                          src={selectedTeacher.image}
                          alt={selectedTeacher.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                    
                    <div className="flex-grow space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground mb-1">Biographie</h4>
                        <p className="text-sm">{selectedTeacher.longBio}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground mb-1">Domaines d'expertise</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedTeacher.expertise.map((skill, index) => (
                            <span key={index} className="px-2 py-1 bg-muted text-xs rounded-full">{skill}</span>
                          ))}
                        </div>
                      </div>
                      
                      {selectedTeacher.achievements && (
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-1">Réalisations</h4>
                          <ul className="list-disc list-inside text-sm">
                            {selectedTeacher.achievements.map((achievement, index) => (
                              <li key={index}>{achievement}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {selectedTeacher.languages && (
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-1">Langues</h4>
                          <div className="flex flex-wrap gap-2">
                            {selectedTeacher.languages.map((language, index) => (
                              <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">{language}</span>
                            ))}
                          </div>
                        </div>
                      )}

                      {selectedTeacher.social && (
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-2">Suivez-moi</h4>
                          <div className="flex gap-3">
                            {selectedTeacher.social.linkedin && (
                              <Link
                                href={selectedTeacher.social.linkedin}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="p-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors"
                              >
                                <Linkedin size={16} />
                              </Link>
                            )}
                            {selectedTeacher.social.youtube && (
                              <Link
                                href={selectedTeacher.social.youtube}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                              >
                                <Youtube size={16} />
                              </Link>
                            )}
                            {selectedTeacher.social.facebook && (
                              <Link
                                href={selectedTeacher.social.facebook}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                              >
                                <Facebook size={16} />
                              </Link>
                            )}
                            {selectedTeacher.social.instagram && (
                              <Link
                                href={selectedTeacher.social.instagram}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="p-2 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 text-white rounded-full hover:from-purple-600 hover:via-pink-600 hover:to-orange-600 transition-colors"
                              >
                                <Instagram size={16} />
                              </Link>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex justify-end mt-4">
                    <DialogClose asChild>
                      <Button>Fermer</Button>
                    </DialogClose>
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>

        {/* Section expertise et méthode */}
        <motion.div
          className="mx-auto max-w-4xl py-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="grid gap-8 md:grid-cols-2">
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-green-700">Expertise Linguistique</h2>
              <p className="text-muted-foreground">
                Natif d'Oran, notre enseignant a vécu dans plusieurs régions d'Algérie (Alger, Oran, Sidi Bel Abbès, Tlemcen)
                et possède une famille à l'est et au sud du pays. Cette richesse géographique lui permet de proposer un
                <strong> dialecte algérien unifié</strong>, accessible à tous les apprenants.
              </p>
              <p className="text-muted-foreground">
                Grâce à cette expérience multiculturelle, il maîtrise les nuances régionales de l'algérien et peut
                vous enseigner un <strong>darija commun</strong> que vous pourrez utiliser partout en Algérie.
              </p>
            </div>
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-green-700">Méthode Pédagogique</h2>
              <p className="text-muted-foreground">
                Notre approche d'enseignement se base sur <strong>l'immersion progressive</strong> et la pratique
                conversationnelle. Chaque leçon est conçue pour vous faire progresser naturellement, en partant
                de situations concrètes de la vie quotidienne algérienne.
              </p>
              <p className="text-muted-foreground">
                L'enseignement combine <strong>tradition orale</strong> et méthodes modernes, avec un focus sur
                la prononciation authentique et l'usage contextuel des expressions algériennes.
              </p>
            </div>
          </div>

          {/* Section avantages */}
          <div className="mt-12 space-y-6">
            <h2 className="text-2xl font-bold text-center text-green-700">Pourquoi Choisir Notre Enseignant ?</h2>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-green-600 font-bold">🎯</span>
                </div>
                <h3 className="font-semibold">Approche Personnalisée</h3>
                <p className="text-sm text-muted-foreground">
                  Adaptation du rythme et du contenu selon votre niveau et vos objectifs d'apprentissage.
                </p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-green-600 font-bold">🗣️</span>
                </div>
                <h3 className="font-semibold">Pratique Conversationnelle</h3>
                <p className="text-sm text-muted-foreground">
                  Emphasis sur la communication orale et les situations réelles de la vie algérienne.
                </p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-green-600 font-bold">🌍</span>
                </div>
                <h3 className="font-semibold">Culture Authentique</h3>
                <p className="text-sm text-muted-foreground">
                  Immersion dans la culture algérienne à travers la langue, les traditions et les expressions locales.
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}