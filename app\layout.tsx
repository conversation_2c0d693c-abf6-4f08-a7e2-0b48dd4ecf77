import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { SharedBackground } from "@/components/shared-background"
import { JsonLd, getWebsiteJsonLd } from '@/components/json-ld'
import { Analytics } from "@vercel/analytics/next"
import { SpeedInsights } from "@vercel/speed-insights/next"
import { FacebookPixel } from "@/components/facebook-pixel"
// Optimisation des polices avec display swap
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#ffffff',
}

export const metadata: Metadata = {
  metadataBase: new URL('https://parleralgerien.com'),
  title: 'Parler Algérien - <PERSON>pprenez l\'algérien en ligne',
  description: 'Plateforme d\'apprentissage en ligne de l\'algérien avec des cours interactifs, des exercices pratiques et des enseignants natifs. Apprenez le dialecte algérien à votre rythme.',
  generator: 'Next.js',
  keywords: ['algérien', 'cours de langue', 'apprendre l\'algérien', 'dialecte algérien', 'darija', 'cours en ligne', 'langue arabe', 'apprentissage langue', 'formation algérien', 'école arabe algérien', 'communauté algérienne', 'réseaux sociaux algérien'],
  authors: [{ name: 'Parler Algérien', url: 'https://parleralgerien.com' }],
  creator: 'Parler Algérien',
  publisher: 'Parler Algérien',
  openGraph: {
    type: 'website',
    locale: 'fr_FR',
    url: 'https://parleralgerien.com',
    title: 'Parler Algérien - École d\'arabe algérien (darija)',
    description: 'Rejoignez +500 étudiants qui apprennent l\'algérien avec notre méthode unique. Cours interactifs, enseignants natifs, résultats garantis !',
    siteName: 'Parler Algérien',
    images: [
      {
        url: '/images/og-image-placeholder.svg',
        width: 1200,
        height: 630,
        alt: 'Parler Algérien - École d\'arabe algérien (darija) en ligne',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@parleralgerien',
    creator: '@parleralgerien',
    title: 'Parler Algérien - École d\'arabe algérien (darija)',
    description: 'Apprenez l\'algérien avec +500 étudiants satisfaits. Cours interactifs, enseignants natifs, méthode unique !',
    images: ['/images/og-image-placeholder.svg'],
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '16x16 32x32', type: 'image/x-icon' },
      { url: '/favicon.png', sizes: '192x192', type: 'image/png' },
    ],
    apple: [
      { url: '/favicon.png', sizes: '180x180', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
  },
  alternates: {
    canonical: 'https://parleralgerien.com',
  },
}
//New Layout
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="fr" className={inter.variable}>
      <head>
        <link rel="icon" href="/favicon.png" />
        <link rel="apple-touch-icon" href="/favicon.png" />
        <link rel="manifest" href="/manifest.json" />
        {/* Préchargement des polices critiques */}
        <link
          rel="preload"
          href="https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <JsonLd data={getWebsiteJsonLd()} />
      </head>
      <body className={inter.className}>
        
        <div className="flex min-h-screen flex-col items-center justify-center">
          <SharedBackground />
          <div className="relative z-10 flex flex-col min-h-screen w-full max-w-7xl items-center justify-center mx-auto">
            <Header />
            {children}
            <Footer />
          </div>
          <Analytics />
          <SpeedInsights />
          {/* Décommentez et ajoutez votre ID Pixel Facebook */}
          {/* <FacebookPixel pixelId="VOTRE_PIXEL_ID" /> */}
        </div>
      </body>
    </html>
  )
}
